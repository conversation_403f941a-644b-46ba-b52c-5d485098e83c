<?php
/**
 * Router for PHP Built-in Server
 * This handles API routing for the development server
 */

// Get the requested URI
$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Remove leading slash
$uri = ltrim($uri, '/');

// Handle API routes
if (strpos($uri, 'api/') === 0) {
    // Remove 'api/' prefix
    $apiPath = substr($uri, 4);
    
    // Map API routes to actual files
    $routes = [
        'auth/login' => 'api/auth/login.php',
        'auth/validate' => 'api/auth/validate.php',
        'auth/admin-login' => 'api/auth/admin-login.php',
        'user/profile' => 'api/user/profile.php',
        'user/courses' => 'api/user/courses.php',
        'user/progress' => 'api/user/progress.php',
        'videos/watch' => 'api/videos/watch.php',
    ];
    
    if (isset($routes[$apiPath])) {
        $filePath = __DIR__ . '/' . $routes[$apiPath];
        if (file_exists($filePath)) {
            // Set proper headers for API responses
            header('Content-Type: application/json');
            header('Access-Control-Allow-Origin: http://localhost:3000');
            header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
            header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
            
            // Handle preflight OPTIONS requests
            if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
                http_response_code(200);
                exit;
            }
            
            // Include the API file
            require_once __DIR__ . '/config/config.php';
            require_once __DIR__ . '/config/database.php';

            // Only include functions.php if it exists and functions aren't already declared
            if (file_exists(__DIR__ . '/includes/functions.php') && !function_exists('validateRequired')) {
                require_once __DIR__ . '/includes/functions.php';
            }

            require_once __DIR__ . '/includes/Auth.php';
            require_once __DIR__ . '/includes/JWT.php';
            
            // Initialize response array
            $response = [
                'success' => false,
                'message' => 'Unknown error',
                'data' => null
            ];
            
            // Get input data
            $input = [];
            if ($_SERVER['REQUEST_METHOD'] === 'POST' || $_SERVER['REQUEST_METHOD'] === 'PUT') {
                $rawInput = file_get_contents('php://input');
                $input = json_decode($rawInput, true) ?: [];
                
                // Also include form data if available
                $input = array_merge($input, $_POST);
            } else {
                $input = $_GET;
            }
            
            // Include the actual API file
            include $filePath;
            return;
        }
    }
    
    // API route not found
    http_response_code(404);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'API endpoint not found: ' . $apiPath
    ]);
    return;
}

// Handle admin routes
if (strpos($uri, 'admin') === 0) {
    $adminPath = $uri;
    if ($adminPath === 'admin' || $adminPath === 'admin/') {
        $adminPath = 'admin/index.php';
    }
    
    $filePath = __DIR__ . '/' . $adminPath;
    if (file_exists($filePath)) {
        require $filePath;
        return;
    }
}

// Handle static files
$filePath = __DIR__ . '/' . $uri;
if (file_exists($filePath) && !is_dir($filePath)) {
    return false; // Let PHP built-in server handle static files
}

// Default fallback
if ($uri === '' || $uri === '/') {
    echo "Backend API Server is running!<br>";
    echo "API Base URL: http://localhost:8000/api/<br>";
    echo "Admin Panel: <a href='/admin'>http://localhost:8000/admin</a>";
    return;
}

// 404 for everything else
http_response_code(404);
echo "404 - Not Found: " . htmlspecialchars($uri);
?>
