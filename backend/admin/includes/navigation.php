<?php
/**
 * Unified Navigation System
 * Provides consistent navigation across all admin pages
 * Desktop: Side panel navigation
 * Mobile: Bottom navigation bar
 */

class AdminNavigation {
    private $currentPage;
    private $adminName;
    
    public function __construct($currentPage = '', $adminName = '') {
        $this->currentPage = $currentPage;
        $this->adminName = $adminName ?: ($_SESSION['admin_name'] ?? 'Admin');
    }
    
    private function isActive($page) {
        return strpos($this->currentPage, $page) !== false ? 'active' : '';
    }
    
    private function isMobile() {
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $mobileKeywords = [
            'Mobile', 'Android', 'iPhone', 'iPad', 'iPod', 'BlackBerry', 
            'Windows Phone', 'Opera Mini', 'IEMobile', 'Mobile Safari'
        ];
        
        foreach ($mobileKeywords as $keyword) {
            if (stripos($userAgent, $keyword) !== false) {
                return true;
            }
        }
        return false;
    }
    
    public function getNavigationCSS() {
        return '
        <style>
        :root {
            /* Minimalistic Color Palette - Same as dashboard_weightloss.php */
            --primary: #059669;
            --primary-light: #10b981;
            --secondary: #0891b2;
            --accent: #dc2626;
            --success: #16a34a;
            --warning: #ca8a04;
            --info: #0284c7;
            --light: #ffffff;
            --surface: #f9fafb;
            --border: #e5e7eb;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --text-muted: #9ca3af;

            /* Minimalistic Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

            /* Navigation Dimensions */
            --sidebar-width: 280px;
            --bottom-nav-height: 80px;

            /* Consistent Spacing */
            --spacing-xs: 0.5rem;
            --spacing-sm: 0.75rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --spacing-2xl: 3rem;

            /* Consistent Border Radius */
            --radius-sm: 6px;
            --radius: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 20px;
        }

        body {
            font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: var(--surface);
            margin: 0;
            padding: 0;
            line-height: 1.6;
            color: var(--text-primary);
        }

        /* Desktop Sidebar */
        .admin-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: var(--light);
            border-right: 1px solid var(--border);
            z-index: 1000;
            overflow-y: auto;
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: var(--spacing-xl);
            border-bottom: 1px solid var(--border);
        }

        .sidebar-brand {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            text-decoration: none;
            color: var(--text-primary);
        }

        .brand-icon {
            width: 40px;
            height: 40px;
            background: var(--primary);
            color: white;
            border-radius: var(--radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }

        .brand-text {
            font-size: 1.125rem;
            font-weight: 600;
        }

        .sidebar-nav {
            padding: var(--spacing-lg) 0;
        }

        .nav-section {
            margin-bottom: var(--spacing-xl);
        }

        .nav-section-title {
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 0 var(--spacing-xl);
            margin-bottom: var(--spacing-md);
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md) var(--spacing-xl);
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
            border-right: 3px solid transparent;
        }

        .nav-link:hover {
            background: var(--surface);
            color: var(--primary);
            text-decoration: none;
        }

        .nav-link.active {
            background: rgba(5, 150, 105, 0.1);
            color: var(--primary);
            border-right-color: var(--primary);
        }

        .nav-icon {
            width: 20px;
            font-size: 1.125rem;
        }

        /* Main Content Area */
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: margin-left 0.3s ease;
        }

        /* Mobile Bottom Navigation */
        .mobile-bottom-nav {
            display: none;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: var(--bottom-nav-height);
            background: var(--light);
            border-top: 1px solid var(--border);
            z-index: 1000;
            box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .bottom-nav-container {
            display: flex;
            height: 100%;
            align-items: center;
            justify-content: space-around;
            padding: 0 var(--spacing-sm);
        }

        .bottom-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: var(--text-secondary);
            transition: all 0.2s ease;
            padding: var(--spacing-sm);
            border-radius: var(--radius);
            min-width: 60px;
            text-align: center;
        }

        .bottom-nav-item:hover {
            color: var(--primary);
            text-decoration: none;
        }

        .bottom-nav-item.active {
            color: var(--primary);
            background: rgba(5, 150, 105, 0.1);
        }

        .bottom-nav-icon {
            font-size: 1.25rem;
            margin-bottom: var(--spacing-xs);
        }

        .bottom-nav-label {
            font-size: 0.75rem;
            font-weight: 500;
        }

        /* Mobile Styles */
        @media (max-width: 768px) {
            .admin-sidebar {
                transform: translateX(-100%);
            }

            .admin-sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding-bottom: var(--bottom-nav-height);
            }

            .mobile-bottom-nav {
                display: block;
            }

            .mobile-menu-btn {
                display: block;
                background: none;
                border: none;
                font-size: 1.5rem;
                color: var(--primary);
                cursor: pointer;
                padding: var(--spacing-sm);
            }
        }

        @media (min-width: 769px) {
            .mobile-menu-btn {
                display: none;
            }
        }

        /* Overlay for mobile sidebar */
        .sidebar-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        .sidebar-overlay.active {
            display: block;
        }
        </style>';
    }
    
    public function renderNavigation() {
        $isMobile = $this->isMobile();
        
        return '
        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay"></div>
        
        <!-- Desktop Sidebar -->
        <div class="admin-sidebar" id="adminSidebar">
            <div class="sidebar-header">
                <a href="dashboard_weightloss.php" class="sidebar-brand">
                    <div class="brand-icon">
                        <i class="bi bi-heart-pulse"></i>
                    </div>
                    <div class="brand-text">HomeWorkout Pro</div>
                </a>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <a href="dashboard_weightloss.php" class="nav-link ' . $this->isActive('dashboard') . '">
                        <i class="bi bi-speedometer2 nav-icon"></i>
                        Dashboard
                    </a>
                    <a href="users_modern.php" class="nav-link ' . $this->isActive('users') . '">
                        <i class="bi bi-people-fill nav-icon"></i>
                        Users
                    </a>
                    <a href="courses.php" class="nav-link ' . $this->isActive('courses') . '">
                        <i class="bi bi-play-circle-fill nav-icon"></i>
                        Courses
                    </a>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">Analytics</div>
                    <a href="analytics.php" class="nav-link ' . $this->isActive('analytics') . '">
                        <i class="bi bi-graph-up nav-icon"></i>
                        Analytics
                    </a>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <a href="settings.php" class="nav-link ' . $this->isActive('settings') . '">
                        <i class="bi bi-gear-fill nav-icon"></i>
                        Settings
                    </a>
                    <a href="logout.php" class="nav-link">
                        <i class="bi bi-box-arrow-right nav-icon"></i>
                        Logout
                    </a>
                </div>
            </nav>
        </div>

        <!-- Mobile Bottom Navigation -->
        <div class="mobile-bottom-nav">
            <div class="bottom-nav-container">
                <a href="dashboard_weightloss.php" class="bottom-nav-item ' . $this->isActive('dashboard') . '">
                    <i class="bi bi-speedometer2 bottom-nav-icon"></i>
                    <span class="bottom-nav-label">Dashboard</span>
                </a>
                <a href="users_modern.php" class="bottom-nav-item ' . $this->isActive('users') . '">
                    <i class="bi bi-people-fill bottom-nav-icon"></i>
                    <span class="bottom-nav-label">Users</span>
                </a>
                <a href="courses.php" class="bottom-nav-item ' . $this->isActive('courses') . '">
                    <i class="bi bi-play-circle-fill bottom-nav-icon"></i>
                    <span class="bottom-nav-label">Courses</span>
                </a>
                <a href="analytics.php" class="bottom-nav-item ' . $this->isActive('analytics') . '">
                    <i class="bi bi-graph-up bottom-nav-icon"></i>
                    <span class="bottom-nav-label">Analytics</span>
                </a>
                <a href="logout.php" class="bottom-nav-item">
                    <i class="bi bi-box-arrow-right bottom-nav-icon"></i>
                    <span class="bottom-nav-label">Logout</span>
                </a>
            </div>
        </div>';
    }
    
    public function getNavigationJS() {
        return '
        <script>
        document.addEventListener("DOMContentLoaded", function() {
            const sidebar = document.getElementById("adminSidebar");
            const overlay = document.getElementById("sidebarOverlay");
            const menuBtns = document.querySelectorAll(".mobile-menu-btn");
            
            // Mobile menu toggle
            menuBtns.forEach(btn => {
                btn.addEventListener("click", function() {
                    sidebar.classList.toggle("mobile-open");
                    overlay.classList.toggle("active");
                });
            });
            
            // Close sidebar when clicking overlay
            overlay.addEventListener("click", function() {
                sidebar.classList.remove("mobile-open");
                overlay.classList.remove("active");
            });
            
            // Close sidebar when clicking nav link on mobile
            const navLinks = document.querySelectorAll(".nav-link");
            navLinks.forEach(link => {
                link.addEventListener("click", function() {
                    if (window.innerWidth <= 768) {
                        sidebar.classList.remove("mobile-open");
                        overlay.classList.remove("active");
                    }
                });
            });
            
            // Handle window resize
            window.addEventListener("resize", function() {
                if (window.innerWidth > 768) {
                    sidebar.classList.remove("mobile-open");
                    overlay.classList.remove("active");
                }
            });
        });
        </script>';
    }
}
?>
