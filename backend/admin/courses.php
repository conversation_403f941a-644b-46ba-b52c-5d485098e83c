<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/Auth.php';
require_once __DIR__ . '/includes/navigation.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Initialize navigation
$navigation = new AdminNavigation('courses.php', $_SESSION['admin_name'] ?? 'Admin');

$auth = new Auth();
$database = new Database();
$db = $database->getConnection();

$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add_course':
            $title = sanitizeInput($_POST['title']);
            $description = sanitizeInput($_POST['description']);
            $durationDays = (int)$_POST['duration_days'];
            $videoUnlockInterval = (int)$_POST['video_unlock_interval'];
            
            if (!empty($title)) {
                try {
                    $stmt = $db->prepare("
                        INSERT INTO courses (title, description, duration_days, video_unlock_interval, created_by) 
                        VALUES (?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([$title, $description, $durationDays, $videoUnlockInterval, $_SESSION['admin_id']]);
                    
                    $message = 'Course added successfully!';
                    $messageType = 'success';
                    
                } catch (Exception $e) {
                    $message = 'Error adding course: ' . $e->getMessage();
                    $messageType = 'danger';
                }
            } else {
                $message = 'Course title is required';
                $messageType = 'danger';
            }
            break;
    }
}

// Get all courses
$stmt = $db->query("
    SELECT c.*, 
           a.username as created_by_name,
           COUNT(cv.id) as video_count,
           COUNT(uc.id) as assigned_users
    FROM courses c
    LEFT JOIN admins a ON c.created_by = a.id
    LEFT JOIN course_videos cv ON c.id = cv.course_id AND cv.is_active = 1
    LEFT JOIN user_courses uc ON c.id = uc.course_id AND uc.is_active = 1
    GROUP BY c.id, c.title, c.description, c.duration_days, c.video_unlock_interval, 
             c.is_active, c.created_by, c.created_at, c.updated_at, a.username
    ORDER BY c.created_at DESC
");
$courses = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Course Management - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <?= $navigation->getNavigationCSS() ?>
    <style>
        /* Use same minimalistic theme as dashboard_weightloss.php */
        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .main-card {
            background: var(--light);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
            overflow: hidden;
            margin-bottom: var(--spacing-xl);
        }

        .header-section {
            background: var(--light);
            border-bottom: 1px solid var(--border);
            padding: var(--spacing-2xl) var(--spacing-xl);
            position: relative;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 700;
            margin: 0 0 var(--spacing-xs) 0;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .dashboard-title i {
            color: var(--primary);
            font-size: 1.75rem;
        }

        .dashboard-subtitle {
            font-size: 1rem;
            margin: 0;
            font-weight: 400;
            color: var(--text-secondary);
        }

        .content-section {
            padding: var(--spacing-xl);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 var(--spacing-lg) 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .section-title i {
            color: var(--primary);
            font-size: 1.125rem;
        }

        .form-container {
            background: var(--light);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border);
        }

        .table-container {
            background: var(--light);
            border-radius: var(--radius-md);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border);
        }

        .btn-minimal {
            background: var(--light);
            color: var(--text-primary);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: var(--spacing-sm) var(--spacing-md);
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .btn-minimal:hover {
            background: var(--surface);
            color: var(--primary);
            text-decoration: none;
            box-shadow: var(--shadow-sm);
        }

        .btn-primary {
            background: var(--primary);
            color: white;
            border: 1px solid var(--primary);
        }

        .btn-primary:hover {
            background: var(--primary-light);
            border-color: var(--primary-light);
            color: white;
        }

        @media (max-width: 768px) {
            .dashboard-container {
                padding: var(--spacing-md);
            }

            .header-section {
                padding: var(--spacing-xl) var(--spacing-md);
            }

            .content-section {
                padding: var(--spacing-md);
            }
        }
    </style>
</head>
<body>
    <?= $navigation->renderNavigation() ?>

    <div class="main-content">
        <div class="dashboard-container">
            <div class="main-card">
                <!-- Header Section -->
                <div class="header-section">
                    <div class="header-content">
                        <div>
                            <button class="mobile-menu-btn" type="button">
                                <i class="bi bi-list"></i>
                            </button>
                            <h1 class="dashboard-title">
                                <i class="bi bi-play-circle-fill"></i>Course Management
                            </h1>
                            <p class="dashboard-subtitle">Manage courses and video content</p>
                        </div>
                    </div>
                </div>

                <!-- Content Section -->
                <div class="content-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="section-title">
                            <i class="bi bi-collection-play"></i>
                            All Courses
                        </h2>
                        <button type="button" class="btn-minimal btn-primary" data-bs-toggle="modal" data-bs-target="#addCourseModal">
                            <i class="bi bi-plus-circle"></i>
                            Add New Course
                        </button>
                    </div>

                    <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php endif; ?>

                    <!-- Courses Table -->
                    <div class="table-container">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle mb-0">
                                <thead>
                                    <tr>
                                        <th>Course Title</th>
                                        <th>Duration</th>
                                        <th>Videos</th>
                                        <th>Assigned Users</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($courses)): ?>
                                    <tr>
                                        <td colspan="7" class="text-center text-muted">
                                            No courses found. <a href="#" data-bs-toggle="modal" data-bs-target="#addCourseModal">Create your first course</a>
                                        </td>
                                    </tr>
                                    <?php else: ?>
                                    <?php foreach ($courses as $course): ?>
                                    <tr>
                                        <td>
                                            <div>
                                                <div class="fw-bold"><?php echo htmlspecialchars($course['title']); ?></div>
                                                <?php if ($course['description']): ?>
                                                <small class="text-muted"><?php echo htmlspecialchars(substr($course['description'], 0, 100)); ?>...</small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td><?php echo $course['duration_days']; ?> days</td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $course['video_count']; ?> videos</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success"><?php echo $course['assigned_users']; ?> users</span>
                                        </td>
                                        <td>
                                            <?php if ($course['is_active']): ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Inactive</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('M j, Y', strtotime($course['created_at'])); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-video"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add Course Modal -->
    <div class="modal fade" id="addCourseModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Course</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_course">
                        
                        <div class="mb-3">
                            <label for="title" class="form-label">Course Title *</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="duration_days" class="form-label">Duration (Days)</label>
                                    <input type="number" class="form-control" id="duration_days" name="duration_days" value="56" min="1">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="video_unlock_interval" class="form-label">Video Unlock Interval (Days)</label>
                                    <input type="number" class="form-control" id="video_unlock_interval" name="video_unlock_interval" value="8" min="1">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Course</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <?= $navigation->getNavigationJS() ?>
</body>
</html>
