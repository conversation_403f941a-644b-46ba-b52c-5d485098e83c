<?php
session_start();
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/Auth.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// Get user ID from URL
$userId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($userId === 0) {
    header('Location: users.php');
    exit;
}

$database = new Database();
$db = $database->getConnection();
$auth = new Auth();

// Fetch user details
$stmt = $db->prepare("
    SELECT u.*, 
           c.title as course_title,
           c.description as course_description,
           c.duration_days,
           c.video_unlock_interval
    FROM users u 
    LEFT JOIN courses c ON u.course_id = c.id 
    WHERE u.id = ?
");
$stmt->execute([$userId]);
$user = $stmt->fetch();

if (!$user) {
    header('Location: users.php');
    exit;
}

// Fetch user's video progress
$stmt = $db->prepare("
    SELECT vp.*, cv.title as video_title, cv.vimeo_video_id, cv.unlock_day, cv.duration_seconds
    FROM video_progress vp
    JOIN course_videos cv ON vp.video_id = cv.id
    WHERE vp.user_id = ?
    ORDER BY cv.unlock_day ASC
");
$stmt->execute([$userId]);
$videoProgress = $stmt->fetchAll();

// Fetch user sessions
$stmt = $db->prepare("
    SELECT * FROM user_sessions 
    WHERE user_id = ? 
    ORDER BY created_at DESC 
    LIMIT 10
");
$stmt->execute([$userId]);
$sessions = $stmt->fetchAll();

// Calculate BMI
function calculateBMI($weight, $height) {
    if ($weight && $height && $height > 0) {
        $heightInMeters = $height / 100;
        return $weight / ($heightInMeters * $heightInMeters);
    }
    return null;
}

function getBMICategory($bmi) {
    if ($bmi === null) return 'Unknown';
    if ($bmi < 18.5) return 'Underweight';
    if ($bmi < 25) return 'Normal';
    if ($bmi < 30) return 'Overweight';
    return 'Obese';
}

function getBMIColor($bmi) {
    if ($bmi === null) return '#6c757d';
    if ($bmi < 18.5) return '#0dcaf0';
    if ($bmi < 25) return '#198754';
    if ($bmi < 30) return '#fd7e14';
    return '#dc3545';
}

$bmi = calculateBMI($user['weight'], $user['height']);
$bmiCategory = getBMICategory($bmi);
$bmiColor = getBMIColor($bmi);

// Calculate progress statistics
$totalVideos = count($videoProgress);
$completedVideos = array_filter($videoProgress, function($vp) {
    return $vp['is_completed'] == 1;
});
$completedCount = count($completedVideos);
$progressPercentage = $totalVideos > 0 ? ($completedCount / $totalVideos) * 100 : 0;

// Calculate current week
$currentWeek = 1;
if ($user['created_at']) {
    $startDate = new DateTime($user['created_at']);
    $now = new DateTime();
    $daysSinceStart = $now->diff($startDate)->days;
    $currentWeek = floor($daysSinceStart / ($user['video_unlock_interval'] ?: 8)) + 1;
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_profile':
                $fullName = sanitizeInput($_POST['full_name']);
                $email = sanitizeInput($_POST['email']);
                $phone = sanitizeInput($_POST['phone']);
                $age = (int)$_POST['age'];
                $weight = (float)$_POST['weight'];
                $height = (float)$_POST['height'];
                $targetWeight = (float)$_POST['target_weight'];
                
                $stmt = $db->prepare("
                    UPDATE users SET 
                        full_name = ?, email = ?, phone = ?, age = ?, 
                        weight = ?, height = ?, target_weight = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                
                if ($stmt->execute([$fullName, $email, $phone, $age, $weight, $height, $targetWeight, $userId])) {
                    $message = 'Profile updated successfully!';
                    $messageType = 'success';
                    // Refresh user data
                    $stmt = $db->prepare("SELECT u.*, c.title as course_title FROM users u LEFT JOIN courses c ON u.course_id = c.id WHERE u.id = ?");
                    $stmt->execute([$userId]);
                    $user = $stmt->fetch();
                    $bmi = calculateBMI($user['weight'], $user['height']);
                    $bmiCategory = getBMICategory($bmi);
                    $bmiColor = getBMIColor($bmi);
                } else {
                    $message = 'Error updating profile!';
                    $messageType = 'danger';
                }
                break;
                
            case 'toggle_status':
                $newStatus = $user['is_active'] ? 0 : 1;
                $stmt = $db->prepare("UPDATE users SET is_active = ?, updated_at = NOW() WHERE id = ?");
                if ($stmt->execute([$newStatus, $userId])) {
                    $user['is_active'] = $newStatus;
                    $message = $newStatus ? 'User activated successfully!' : 'User deactivated successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Error updating user status!';
                    $messageType = 'danger';
                }
                break;
                
            case 'generate_auth_link':
                $authLink = $auth->generateAuthLink($userId, 1);
                if ($authLink) {
                    $message = 'Authentication link generated successfully!';
                    $messageType = 'success';
                    $generatedAuthLink = $authLink;
                } else {
                    $message = 'Error generating authentication link!';
                    $messageType = 'danger';
                }
                break;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Profile - <?= htmlspecialchars($user['full_name']) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px;
            overflow: hidden;
        }
        
        .header-section {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 2rem;
            position: relative;
        }
        
        .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.1;
        }
        
        .profile-avatar {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1rem;
            border: 4px solid rgba(255, 255, 255, 0.3);
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .progress-ring {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background: conic-gradient(var(--primary-color) <?= $progressPercentage * 3.6 ?>deg, #e5e7eb 0deg);
        }
        
        .progress-ring::before {
            content: '';
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: white;
            position: absolute;
        }
        
        .progress-text {
            position: relative;
            z-index: 1;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .section-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }
        
        .section-header {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            padding: 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .btn-premium {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }
        
        .btn-premium:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
            color: white;
        }
        
        .video-progress-item {
            padding: 1rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: background-color 0.3s ease;
        }
        
        .video-progress-item:hover {
            background-color: rgba(99, 102, 241, 0.05);
        }
        
        .video-progress-item:last-child {
            border-bottom: none;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .badge-active {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }
        
        .badge-inactive {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
        }
        
        .nav-tabs .nav-link {
            border: none;
            border-radius: 10px 10px 0 0;
            margin-right: 0.5rem;
            color: #6b7280;
            font-weight: 600;
        }
        
        .nav-tabs .nav-link.active {
            background: var(--primary-color);
            color: white;
        }
        
        .table th {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border: none;
            font-weight: 600;
            color: #374151;
        }
        
        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            border-radius: 10px;
            padding: 0.5rem 1rem;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header Section -->
            <div class="header-section">
                <div class="d-flex justify-content-between align-items-start mb-4">
                    <a href="users.php" class="back-btn">
                        <i class="bi bi-arrow-left me-2"></i>Back to Users
                    </a>
                    <div class="d-flex gap-2">
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="action" value="toggle_status">
                            <button type="submit" class="btn btn-outline-light btn-sm">
                                <i class="bi bi-<?= $user['is_active'] ? 'pause' : 'play' ?>-fill me-1"></i>
                                <?= $user['is_active'] ? 'Deactivate' : 'Activate' ?>
                            </button>
                        </form>
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="action" value="generate_auth_link">
                            <button type="submit" class="btn btn-outline-light btn-sm">
                                <i class="bi bi-link-45deg me-1"></i>Generate Link
                            </button>
                        </form>
                    </div>
                </div>
                
                <div class="row align-items-center">
                    <div class="col-md-2">
                        <div class="profile-avatar mx-auto">
                            <?= strtoupper(substr($user['full_name'], 0, 2)) ?>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h1 class="mb-2"><?= htmlspecialchars($user['full_name']) ?></h1>
                        <p class="mb-1 opacity-75">
                            <i class="bi bi-envelope me-2"></i><?= htmlspecialchars($user['email']) ?>
                        </p>
                        <?php if ($user['phone']): ?>
                        <p class="mb-1 opacity-75">
                            <i class="bi bi-telephone me-2"></i><?= htmlspecialchars($user['phone']) ?>
                        </p>
                        <?php endif; ?>
                        <p class="mb-0 opacity-75">
                            <i class="bi bi-calendar me-2"></i>Member since <?= date('M d, Y', strtotime($user['created_at'])) ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="d-flex justify-content-end align-items-center gap-3">
                            <div class="progress-ring">
                                <div class="progress-text"><?= number_format($progressPercentage, 0) ?>%</div>
                            </div>
                            <div>
                                <div class="status-badge <?= $user['is_active'] ? 'badge-active' : 'badge-inactive' ?>">
                                    <?= $user['is_active'] ? 'Active' : 'Inactive' ?>
                                </div>
                                <div class="mt-2 text-white-50">
                                    <small>Last login: <?= $user['last_login'] ? date('M d, Y H:i', strtotime($user['last_login'])) : 'Never' ?></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            <?php if ($message): ?>
            <div class="container mt-3">
                <div class="alert alert-<?= $messageType ?> alert-dismissible fade show" role="alert">
                    <?= $message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
            <?php endif; ?>

            <!-- Generated Auth Link -->
            <?php if (isset($generatedAuthLink)): ?>
            <div class="container mt-3">
                <div class="alert alert-info" role="alert">
                    <h6><i class="bi bi-link-45deg me-2"></i>Generated Authentication Link:</h6>
                    <div class="input-group mt-2">
                        <input type="text" class="form-control" id="authLink" value="<?= htmlspecialchars($generatedAuthLink) ?>" readonly>
                        <button class="btn btn-outline-primary" type="button" onclick="copyToClipboard('authLink')">
                            <i class="bi bi-clipboard"></i> Copy
                        </button>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Main Content -->
        <div class="container mt-4">
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="stat-card text-center">
                        <div class="stat-icon mx-auto" style="background: rgba(99, 102, 241, 0.1); color: var(--primary-color);">
                            <i class="bi bi-calendar-week"></i>
                        </div>
                        <h3 class="mb-1" style="color: var(--primary-color);">Week <?= $currentWeek ?></h3>
                        <p class="text-muted mb-0">Current Week</p>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card text-center">
                        <div class="stat-icon mx-auto" style="background: rgba(16, 185, 129, 0.1); color: var(--success-color);">
                            <i class="bi bi-play-circle-fill"></i>
                        </div>
                        <h3 class="mb-1" style="color: var(--success-color);"><?= $completedCount ?>/<?= $totalVideos ?></h3>
                        <p class="text-muted mb-0">Videos Completed</p>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card text-center">
                        <div class="stat-icon mx-auto" style="background: rgba(245, 158, 11, 0.1); color: var(--warning-color);">
                            <i class="bi bi-speedometer2"></i>
                        </div>
                        <h3 class="mb-1" style="color: <?= $bmiColor ?>;">
                            <?= $bmi ? number_format($bmi, 1) : '--' ?>
                        </h3>
                        <p class="text-muted mb-0">BMI (<?= $bmiCategory ?>)</p>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card text-center">
                        <div class="stat-icon mx-auto" style="background: rgba(6, 182, 212, 0.1); color: var(--info-color);">
                            <i class="bi bi-trophy"></i>
                        </div>
                        <h3 class="mb-1" style="color: var(--info-color);"><?= number_format($progressPercentage, 0) ?>%</h3>
                        <p class="text-muted mb-0">Progress</p>
                    </div>
                </div>
            </div>

            <!-- Tabbed Content -->
            <div class="section-card">
                <ul class="nav nav-tabs" id="userTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button" role="tab">
                            <i class="bi bi-person me-2"></i>Profile Information
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="progress-tab" data-bs-toggle="tab" data-bs-target="#progress" type="button" role="tab">
                            <i class="bi bi-graph-up me-2"></i>Video Progress
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="sessions-tab" data-bs-toggle="tab" data-bs-target="#sessions" type="button" role="tab">
                            <i class="bi bi-clock-history me-2"></i>Login Sessions
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="course-tab" data-bs-toggle="tab" data-bs-target="#course" type="button" role="tab">
                            <i class="bi bi-book me-2"></i>Course Details
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="userTabsContent">
                    <!-- Profile Information Tab -->
                    <div class="tab-pane fade show active" id="profile" role="tabpanel">
                        <div class="p-4">
                            <form method="POST">
                                <input type="hidden" name="action" value="update_profile">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="full_name" class="form-label fw-bold">Full Name</label>
                                        <input type="text" class="form-control" id="full_name" name="full_name"
                                               value="<?= htmlspecialchars($user['full_name']) ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label fw-bold">Email</label>
                                        <input type="email" class="form-control" id="email" name="email"
                                               value="<?= htmlspecialchars($user['email']) ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label fw-bold">Phone</label>
                                        <input type="text" class="form-control" id="phone" name="phone"
                                               value="<?= htmlspecialchars($user['phone'] ?? '') ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="age" class="form-label fw-bold">Age</label>
                                        <input type="number" class="form-control" id="age" name="age"
                                               value="<?= $user['age'] ?>" min="1" max="120">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="height" class="form-label fw-bold">Height (cm)</label>
                                        <input type="number" class="form-control" id="height" name="height"
                                               value="<?= $user['height'] ?>" step="0.1" min="1">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="weight" class="form-label fw-bold">Weight (kg)</label>
                                        <input type="number" class="form-control" id="weight" name="weight"
                                               value="<?= $user['weight'] ?>" step="0.1" min="1">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="target_weight" class="form-label fw-bold">Target Weight (kg)</label>
                                        <input type="number" class="form-control" id="target_weight" name="target_weight"
                                               value="<?= $user['target_weight'] ?>" step="0.1" min="1">
                                    </div>
                                </div>

                                <!-- BMI Information -->
                                <?php if ($bmi): ?>
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="alert" style="background: rgba(<?= $bmiColor === '#198754' ? '25, 135, 84' : ($bmiColor === '#fd7e14' ? '253, 126, 20' : ($bmiColor === '#dc3545' ? '220, 53, 69' : '108, 117, 125')) ?>, 0.1); border: 1px solid <?= $bmiColor ?>; color: <?= $bmiColor ?>;">
                                            <h6><i class="bi bi-info-circle me-2"></i>BMI Information</h6>
                                            <p class="mb-0">
                                                Current BMI: <strong><?= number_format($bmi, 1) ?></strong> -
                                                Category: <strong><?= $bmiCategory ?></strong>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <div class="text-end">
                                    <button type="submit" class="btn btn-premium">
                                        <i class="bi bi-save me-2"></i>Update Profile
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Video Progress Tab -->
                    <div class="tab-pane fade" id="progress" role="tabpanel">
                        <div class="p-4">
                            <?php if (empty($videoProgress)): ?>
                            <div class="text-center py-5">
                                <i class="bi bi-play-circle display-1 text-muted"></i>
                                <h4 class="mt-3 text-muted">No Video Progress</h4>
                                <p class="text-muted">This user hasn't started watching any videos yet.</p>
                            </div>
                            <?php else: ?>
                            <div class="mb-3">
                                <h5>Video Progress Overview</h5>
                                <div class="progress mb-3" style="height: 10px;">
                                    <div class="progress-bar" role="progressbar"
                                         style="width: <?= $progressPercentage ?>%; background: var(--primary-color);">
                                    </div>
                                </div>
                                <p class="text-muted">
                                    <?= $completedCount ?> of <?= $totalVideos ?> videos completed
                                    (<?= number_format($progressPercentage, 1) ?>%)
                                </p>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Video</th>
                                            <th>Unlock Day</th>
                                            <th>Duration</th>
                                            <th>Progress</th>
                                            <th>Status</th>
                                            <th>Last Watched</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($videoProgress as $vp): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-play-circle-fill me-2 text-primary"></i>
                                                    <strong><?= htmlspecialchars($vp['video_title']) ?></strong>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">Day <?= $vp['unlock_day'] ?></span>
                                            </td>
                                            <td>
                                                <?= gmdate("i:s", $vp['duration_seconds']) ?>
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 6px; width: 80px;">
                                                    <div class="progress-bar" style="width: <?= $vp['completion_percentage'] ?>%; background: var(--success-color);"></div>
                                                </div>
                                                <small class="text-muted"><?= number_format($vp['completion_percentage'], 1) ?>%</small>
                                            </td>
                                            <td>
                                                <?php if ($vp['is_completed']): ?>
                                                    <span class="badge bg-success">Completed</span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">In Progress</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('M d, Y H:i', strtotime($vp['last_watched_at'])) ?>
                                                </small>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Login Sessions Tab -->
                    <div class="tab-pane fade" id="sessions" role="tabpanel">
                        <div class="p-4">
                            <?php if (empty($sessions)): ?>
                            <div class="text-center py-5">
                                <i class="bi bi-clock-history display-1 text-muted"></i>
                                <h4 class="mt-3 text-muted">No Login Sessions</h4>
                                <p class="text-muted">This user hasn't logged in yet.</p>
                            </div>
                            <?php else: ?>
                            <h5 class="mb-3">Recent Login Sessions</h5>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Session Token</th>
                                            <th>Device Info</th>
                                            <th>IP Address</th>
                                            <th>Created At</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($sessions as $session): ?>
                                        <tr>
                                            <td>
                                                <code class="text-muted"><?= substr($session['session_token'], 0, 20) ?>...</code>
                                            </td>
                                            <td>
                                                <?php
                                                $deviceInfo = json_decode($session['device_info'], true);
                                                if ($deviceInfo && isset($deviceInfo['platform'])) {
                                                    echo '<span class="badge bg-info">' . htmlspecialchars($deviceInfo['platform']) . '</span>';
                                                } else {
                                                    echo '<span class="text-muted">Unknown</span>';
                                                }
                                                ?>
                                            </td>
                                            <td><?= htmlspecialchars($session['ip_address']) ?></td>
                                            <td><?= date('M d, Y H:i:s', strtotime($session['created_at'])) ?></td>
                                            <td>
                                                <span class="badge bg-success">Active</span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Course Details Tab -->
                    <div class="tab-pane fade" id="course" role="tabpanel">
                        <div class="p-4">
                            <?php if ($user['course_title']): ?>
                            <div class="row">
                                <div class="col-md-8">
                                    <h5><?= htmlspecialchars($user['course_title']) ?></h5>
                                    <p class="text-muted"><?= htmlspecialchars($user['course_description']) ?></p>

                                    <div class="row mt-4">
                                        <div class="col-md-6">
                                            <div class="stat-card">
                                                <div class="d-flex align-items-center">
                                                    <div class="stat-icon me-3" style="background: rgba(99, 102, 241, 0.1); color: var(--primary-color);">
                                                        <i class="bi bi-calendar-range"></i>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">Duration</h6>
                                                        <p class="text-muted mb-0"><?= $user['duration_days'] ?> days</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="stat-card">
                                                <div class="d-flex align-items-center">
                                                    <div class="stat-icon me-3" style="background: rgba(16, 185, 129, 0.1); color: var(--success-color);">
                                                        <i class="bi bi-unlock"></i>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">Unlock Interval</h6>
                                                        <p class="text-muted mb-0">Every <?= $user['video_unlock_interval'] ?> days</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="progress-ring mb-3">
                                            <div class="progress-text"><?= number_format($progressPercentage, 0) ?>%</div>
                                        </div>
                                        <h6>Course Progress</h6>
                                        <p class="text-muted">Week <?= $currentWeek ?> of 4</p>
                                    </div>
                                </div>
                            </div>
                            <?php else: ?>
                            <div class="text-center py-5">
                                <i class="bi bi-book display-1 text-muted"></i>
                                <h4 class="mt-3 text-muted">No Course Assigned</h4>
                                <p class="text-muted">This user hasn't been assigned to any course yet.</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            element.select();
            element.setSelectionRange(0, 99999);

            try {
                document.execCommand('copy');
                showToast('Copied to clipboard!', 'success');
            } catch (err) {
                navigator.clipboard.writeText(element.value).then(function() {
                    showToast('Copied to clipboard!', 'success');
                }).catch(function(err) {
                    showToast('Failed to copy to clipboard', 'error');
                });
            }
        }

        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'error' ? 'danger' : 'success'} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 250px;';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            document.body.appendChild(toast);

            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
