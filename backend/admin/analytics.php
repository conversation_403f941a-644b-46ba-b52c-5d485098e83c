<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/Auth.php';
require_once __DIR__ . '/includes/navigation.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Initialize navigation
$navigation = new AdminNavigation('analytics.php', $_SESSION['admin_name'] ?? 'Admin');

$database = new Database();
$db = $database->getConnection();

// Get analytics data
$analytics = [];

// User statistics
$stmt = $db->query("
    SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_users,
        COUNT(CASE WHEN last_login IS NOT NULL THEN 1 END) as users_logged_in
    FROM users
");
$analytics['users'] = $stmt->fetch();

// Course statistics
$stmt = $db->query("
    SELECT 
        COUNT(*) as total_courses,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_courses
    FROM courses
");
$analytics['courses'] = $stmt->fetch();

// Video statistics
$stmt = $db->query("
    SELECT 
        COUNT(*) as total_videos,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_videos
    FROM course_videos
");
$analytics['videos'] = $stmt->fetch();

// Activity statistics (last 30 days)
$stmt = $db->query("
    SELECT 
        COUNT(*) as total_activities,
        COUNT(DISTINCT user_id) as active_users_30d,
        SUM(duration_seconds) as total_watch_time
    FROM user_activity_log 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
");
$analytics['activity'] = $stmt->fetch();

// Recent activity
$stmt = $db->query("
    SELECT 
        u.full_name,
        ual.activity_type,
        ual.duration_seconds,
        ual.created_at,
        cv.title as video_title
    FROM user_activity_log ual
    JOIN users u ON ual.user_id = u.id
    LEFT JOIN course_videos cv ON ual.video_id = cv.id
    ORDER BY ual.created_at DESC
    LIMIT 20
");
$recent_activity = $stmt->fetchAll();

// Daily activity for the last 7 days
$stmt = $db->query("
    SELECT 
        DATE(created_at) as date,
        COUNT(*) as activities,
        COUNT(DISTINCT user_id) as unique_users,
        SUM(duration_seconds) as total_seconds
    FROM user_activity_log
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    GROUP BY DATE(created_at)
    ORDER BY date DESC
");
$daily_activity = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            border-radius: 8px;
            margin: 5px 10px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
        }
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border: none;
            border-radius: 15px;
            color: white;
        }
        .stat-card-2 {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stat-card-3 {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .stat-card-4 {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">Admin Panel</h4>
                        <small class="text-white-50">Weight Loss Dashboard</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.php">
                                <i class="fas fa-users me-2"></i>
                                Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-play-circle me-2"></i>
                                Courses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="analytics.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                Analytics
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="fas fa-cog me-2"></i>
                                Settings
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Analytics Dashboard</h1>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Users</div>
                                        <div class="h5 mb-0 font-weight-bold"><?php echo $analytics['users']['total_users']; ?></div>
                                        <small><?php echo $analytics['users']['active_users']; ?> active</small>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card stat-card-2 h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Courses</div>
                                        <div class="h5 mb-0 font-weight-bold"><?php echo $analytics['courses']['total_courses']; ?></div>
                                        <small><?php echo $analytics['courses']['active_courses']; ?> active</small>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-play-circle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card stat-card-3 h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Videos</div>
                                        <div class="h5 mb-0 font-weight-bold"><?php echo $analytics['videos']['total_videos']; ?></div>
                                        <small><?php echo $analytics['videos']['active_videos']; ?> active</small>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-video fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card stat-card-4 h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Watch Time (30d)</div>
                                        <div class="h5 mb-0 font-weight-bold"><?php echo round(($analytics['activity']['total_watch_time'] ?? 0) / 60); ?> min</div>
                                        <small><?php echo $analytics['activity']['active_users_30d'] ?? 0; ?> active users</small>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-clock fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Activity Charts and Tables -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Daily Activity (Last 7 Days)</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Activities</th>
                                                <th>Unique Users</th>
                                                <th>Watch Time</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($daily_activity as $day): ?>
                                            <tr>
                                                <td><?php echo date('M j, Y', strtotime($day['date'])); ?></td>
                                                <td><?php echo $day['activities']; ?></td>
                                                <td><?php echo $day['unique_users']; ?></td>
                                                <td><?php echo round($day['total_seconds'] / 60); ?> min</td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Recent Activity</h6>
                            </div>
                            <div class="card-body">
                                <?php foreach (array_slice($recent_activity, 0, 10) as $activity): ?>
                                <div class="d-flex align-items-center mb-3">
                                    <div class="me-3">
                                        <?php
                                        $iconClass = 'fas fa-info-circle text-info';
                                        switch($activity['activity_type']) {
                                            case 'login':
                                                $iconClass = 'fas fa-sign-in-alt text-success';
                                                break;
                                            case 'video_start':
                                                $iconClass = 'fas fa-play text-primary';
                                                break;
                                            case 'video_complete':
                                                $iconClass = 'fas fa-check-circle text-success';
                                                break;
                                        }
                                        ?>
                                        <i class="<?php echo $iconClass; ?>"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold"><?php echo htmlspecialchars($activity['full_name'] ?? ''); ?></div>
                                        <small class="text-muted">
                                            <?php echo ucfirst(str_replace('_', ' ', $activity['activity_type'] ?? '')); ?>
                                            <?php if ($activity['video_title']): ?>
                                                - <?php echo htmlspecialchars($activity['video_title']); ?>
                                            <?php endif; ?>
                                        </small>
                                        <div class="text-muted small">
                                            <?php echo date('M j, g:i A', strtotime($activity['created_at'])); ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
