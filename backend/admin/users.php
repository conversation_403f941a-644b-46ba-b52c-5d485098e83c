<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/Auth.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$auth = new Auth();
$database = new Database();
$db = $database->getConnection();

$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add_user':
            $email = sanitizeInput($_POST['email']);
            $phone = sanitizeInput($_POST['phone']);
            $fullName = sanitizeInput($_POST['full_name']);
            $age = (int)$_POST['age'];
            $weight = (float)$_POST['weight'];
            $height = (float)$_POST['height'];
            $targetWeight = (float)$_POST['target_weight'];
            $courseId = (int)$_POST['course_id'];
            
            if (validateEmail($email)) {
                try {
                    $db->beginTransaction();
                    
                    // Generate unique token
                    $uniqueToken = generateUniqueToken(32);
                    
                    // Insert user
                    $stmt = $db->prepare("
                        INSERT INTO users (email, phone, full_name, age, weight, height, target_weight, unique_token, created_by) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([$email, $phone, $fullName, $age, $weight, $height, $targetWeight, $uniqueToken, $_SESSION['admin_id']]);
                    $userId = $db->lastInsertId();
                    
                    // Assign course if selected
                    if ($courseId > 0) {
                        $stmt = $db->prepare("INSERT INTO user_courses (user_id, course_id) VALUES (?, ?)");
                        $stmt->execute([$userId, $courseId]);
                    }
                    
                    $db->commit();
                    $message = 'User added successfully!';
                    $messageType = 'success';
                    
                } catch (Exception $e) {
                    $db->rollback();
                    $message = 'Error adding user: ' . $e->getMessage();
                    $messageType = 'danger';
                }
            } else {
                $message = 'Invalid email address';
                $messageType = 'danger';
            }
            break;
            
        case 'generate_link':
            $userId = (int)$_POST['user_id'];
            $link = $auth->generateAuthLink($userId, $_SESSION['admin_id']);
            if ($link) {
                // Extract token from the link
                $urlParts = parse_url($link);
                parse_str($urlParts['query'], $queryParams);
                $token = $queryParams['token'] ?? '';

                $message = '
                <div class="row">
                    <div class="col-12 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                            <strong>Authentication Link Generated Successfully!</strong>
                        </div>
                    </div>
                    <div class="col-12 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-link-45deg text-primary me-1"></i>
                            Full Authentication Link (Auto-Login):
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-globe"></i></span>
                            <input type="text" class="form-control" id="authLink" value="' . htmlspecialchars($link) . '" readonly>
                            <button class="btn btn-primary" type="button" onclick="copyToClipboard(\'authLink\', \'Link copied!\')">
                                <i class="bi bi-clipboard"></i> Copy Link
                            </button>
                        </div>
                        <small class="text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            Share this link - users click it and are automatically logged in
                        </small>
                    </div>
                    <div class="col-12">
                        <label class="form-label fw-bold">
                            <i class="bi bi-key text-secondary me-1"></i>
                            Access Token (Manual Login):
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-shield-lock"></i></span>
                            <input type="text" class="form-control" id="authToken" value="' . htmlspecialchars($token) . '" readonly>
                            <button class="btn btn-secondary" type="button" onclick="copyToClipboard(\'authToken\', \'Token copied!\')">
                                <i class="bi bi-clipboard"></i> Copy Token
                            </button>
                        </div>
                        <small class="text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            Share this token - users enter it manually in the app login screen
                        </small>
                    </div>
                </div>';
                $messageType = 'success';
            } else {
                $message = 'Error generating authentication link';
                $messageType = 'danger';
            }
            break;
            
        case 'revoke_access':
            $userId = (int)$_POST['user_id'];
            if ($auth->revokeUserAccess($userId)) {
                $message = 'User access revoked successfully';
                $messageType = 'success';
            } else {
                $message = 'Error revoking user access';
                $messageType = 'danger';
            }
            break;
    }
}

// Get all users with their course assignments and progress
$stmt = $db->query("
    SELECT u.*,
           c.title as course_title,
           uc.assigned_at,
           uc.started_at,
           uc.completed_at,
           COUNT(uvp.id) as videos_watched,
           AVG(uvp.completion_percentage) as avg_completion
    FROM users u
    LEFT JOIN user_courses uc ON u.id = uc.user_id AND uc.is_active = 1
    LEFT JOIN courses c ON uc.course_id = c.id
    LEFT JOIN user_video_progress uvp ON u.id = uvp.user_id
    GROUP BY u.id, u.email, u.phone, u.full_name, u.age, u.weight, u.height, u.target_weight,
             u.unique_token, u.device_fingerprint, u.is_active, u.last_login, u.created_by,
             u.created_at, u.updated_at, c.title, uc.assigned_at, uc.started_at, uc.completed_at
    ORDER BY u.created_at DESC
");
$users = $stmt->fetchAll();

// Get all courses for the dropdown
$stmt = $db->query("SELECT id, title FROM courses WHERE is_active = 1 ORDER BY title");
$courses = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - Admin Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f8fafc;
            color: #1e293b;
        }
        .sidebar {
            width: 260px;
            height: 100vh;
            background: #fff;
            border-right: 1px solid #e2e8f0;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1040;
            transition: transform 0.3s ease;
        }
        .sidebar .sidebar-header {
            padding: 2rem 1.5rem 1.5rem;
            border-bottom: 1px solid #e2e8f0;
        }
        .sidebar .sidebar-brand {
            font-size: 1.25rem;
            font-weight: 700;
            color: #6366f1;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        .sidebar .sidebar-brand i {
            width: 32px;
            height: 32px;
            background: #6366f1;
            color: #fff;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
        }
        .sidebar .nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1.5rem;
            color: #64748b;
            text-decoration: none;
            font-weight: 500;
            border: none;
            background: none;
            transition: all 0.2s;
        }
        .sidebar .nav-link.active, .sidebar .nav-link:hover {
            background: #f1f5f9;
            color: #6366f1;
        }
        .sidebar .nav-link i {
            width: 20px;
            font-size: 1.1rem;
        }
        .sidebar .nav-section-title {
            font-size: 0.75rem;
            font-weight: 600;
            color: #94a3b8;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 0 1.5rem;
            margin-bottom: 0.5rem;
        }
        .main-content {
            margin-left: 260px;
            min-height: 100vh;
            transition: margin-left 0.3s ease;
        }
        .topbar {
            background: #fff;
            border-bottom: 1px solid #e2e8f0;
            padding: 1rem 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 1030;
        }
        .topbar .menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #6366f1;
        }
        .topbar .admin-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #6366f1;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.1rem;
        }
        .card {
            border-radius: 16px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.04);
        }
        .table thead th {
            background: #f1f5f9;
            color: #64748b;
            font-weight: 600;
            border: none;
        }
        .table td, .table th {
            vertical-align: middle;
            border: none;
            border-bottom: 1px solid #e2e8f0;
        }
        .avatar {
            width: 38px;
            height: 38px;
            border-radius: 50%;
            background: #6366f1;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1.1rem;
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.7rem;
            border-radius: 8px;
        }
        .progress-bar-custom {
            height: 8px;
            border-radius: 4px;
        }
        .fab {
            position: fixed;
            bottom: 32px;
            right: 32px;
            z-index: 1050;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: #6366f1;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            box-shadow: 0 4px 16px rgba(99,102,241,0.15);
            border: none;
            transition: background 0.2s;
        }
        .fab:hover {
            background: #4f46e5;
        }
        @media (max-width: 991.98px) {
            .sidebar {
                transform: translateX(-100%);
            }
            .sidebar.active {
                transform: translateX(0);
            }
            .main-content {
                margin-left: 0;
            }
            .topbar .menu-btn {
                display: block;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="#" class="sidebar-brand"><i class="bi bi-bar-chart"></i> Admin Panel</a>
        </div>
        <div class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">Main</div>
                <a href="index.php" class="nav-link"><i class="bi bi-house"></i> Dashboard</a>
                <a href="users.php" class="nav-link active"><i class="bi bi-people"></i> Users</a>
                <a href="courses.php" class="nav-link"><i class="bi bi-journal-text"></i> Courses</a>
                <a href="analytics.php" class="nav-link"><i class="bi bi-graph-up"></i> Analytics</a>
                <a href="settings.php" class="nav-link"><i class="bi bi-gear"></i> Settings</a>
            </div>
            <div class="nav-section">
                <div class="nav-section-title">Account</div>
                <a href="logout.php" class="nav-link"><i class="bi bi-box-arrow-right"></i> Logout</a>
            </div>
        </div>
    </div>
    <div class="main-content">
        <div class="topbar">
            <button class="menu-btn" id="menuBtn"><i class="bi bi-list"></i></button>
            <span class="page-title fw-bold fs-4">User Management</span>
            <div class="admin-avatar" title="Admin">
                <?php echo strtoupper(substr($_SESSION['admin_username'] ?? 'A', 0, 2)); ?>
            </div>
        </div>
        <div class="container-fluid py-4">
            <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0 fw-bold">All Users</h6>
                    <button type="button" class="btn btn-primary d-none d-md-inline-flex align-items-center" data-bs-toggle="modal" data-bs-target="#addUserModal">
                        <i class="bi bi-person-plus me-2"></i> Add New User
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table align-middle" id="usersTable">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Course</th>
                                    <th>Progress</th>
                                    <th>Status</th>
                                    <th>Last Login</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar me-3">
                                                <?php echo strtoupper(substr($user['full_name'] ?? 'U', 0, 2)); ?>
                                            </div>
                                            <div>
                                                <div class="fw-semibold"><?php echo htmlspecialchars($user['full_name'] ?? ''); ?></div>
                                                <div class="text-muted small"><?php echo htmlspecialchars($user['phone'] ?? ''); ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($user['email'] ?? ''); ?></td>
                                    <td><?php echo htmlspecialchars($user['course_title'] ?? '-'); ?></td>
                                    <td style="min-width:120px;">
                                        <div class="d-flex align-items-center gap-2">
                                            <div class="flex-grow-1">
                                                <div class="progress progress-bar-custom bg-light">
                                                    <div class="progress-bar bg-primary" role="progressbar" style="width: <?php echo (float)($user['avg_completion'] ?? 0); ?>%"></div>
                                                </div>
                                            </div>
                                            <span class="small text-muted"><?php echo round($user['avg_completion'] ?? 0); ?>%</span>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($user['is_active']): ?>
                                            <span class="badge bg-success status-badge">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary status-badge">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo $user['last_login'] ? date('Y-m-d H:i', strtotime($user['last_login'])) : '-'; ?>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-2">
                                            <form method="post" class="d-inline">
                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                <input type="hidden" name="action" value="generate_link">
                                                <button type="submit" class="btn btn-sm btn-outline-primary" title="Generate Auth Link"><i class="bi bi-link-45deg"></i></button>
                                            </form>
                                            <form method="post" class="d-inline">
                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                <input type="hidden" name="action" value="revoke_access">
                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="Revoke Access"><i class="bi bi-person-x"></i></button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <!-- Floating Action Button for mobile -->
            <button type="button" class="fab d-md-none" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="bi bi-person-plus"></i>
            </button>
        </div>
    </div>
    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <form method="post">
                    <input type="hidden" name="action" value="add_user">
                    <div class="modal-header">
                        <h5 class="modal-title" id="addUserModalLabel">Add New User</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Full Name</label>
                            <input type="text" class="form-control" name="full_name" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Email</label>
                            <input type="email" class="form-control" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Phone</label>
                            <input type="text" class="form-control" name="phone">
                        </div>
                        <div class="row g-2">
                            <div class="col-6 mb-3">
                                <label class="form-label">Age</label>
                                <input type="number" class="form-control" name="age">
                            </div>
                            <div class="col-6 mb-3">
                                <label class="form-label">Course</label>
                                <select class="form-select" name="course_id">
                                    <option value="">Select</option>
                                    <?php foreach ($courses as $course): ?>
                                        <option value="<?php echo $course['id']; ?>"><?php echo htmlspecialchars($course['title']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="row g-2">
                            <div class="col-4 mb-3">
                                <label class="form-label">Weight (kg)</label>
                                <input type="number" step="0.1" class="form-control" name="weight">
                            </div>
                            <div class="col-4 mb-3">
                                <label class="form-label">Height (cm)</label>
                                <input type="number" step="0.1" class="form-control" name="height">
                            </div>
                            <div class="col-4 mb-3">
                                <label class="form-label">Target Weight (kg)</label>
                                <input type="number" step="0.1" class="form-control" name="target_weight">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add User</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#usersTable').DataTable({
                "pageLength": 25,
                "order": [[ 5, "desc" ]],
                "columnDefs": [
                    { "orderable": false, "targets": 6 }
                ]
            });
        });

        // Clipboard functionality
        function copyToClipboard(elementId, successMessage) {
            const element = document.getElementById(elementId);
            if (element) {
                element.select();
                element.setSelectionRange(0, 99999); // For mobile devices

                try {
                    document.execCommand('copy');
                    showToast(successMessage || 'Copied to clipboard!');
                } catch (err) {
                    // Fallback for modern browsers
                    navigator.clipboard.writeText(element.value).then(function() {
                        showToast(successMessage || 'Copied to clipboard!');
                    }).catch(function(err) {
                        console.error('Could not copy text: ', err);
                        showToast('Failed to copy to clipboard', 'error');
                    });
                }
            }
        }

        // Toast notification function
        function showToast(message, type = 'success') {
            // Create toast element
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'error' ? 'danger' : 'success'} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 250px;';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            // Add to page
            document.body.appendChild(toast);

            // Auto remove after 3 seconds
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 3000);
        }

        // Sidebar toggle for mobile
        const sidebar = document.getElementById('sidebar');
        const menuBtn = document.getElementById('menuBtn');
        menuBtn.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });
    </script>
</body>
</html>
