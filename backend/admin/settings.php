<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/Auth.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'update_profile':
            $username = sanitizeInput($_POST['username']);
            $email = sanitizeInput($_POST['email']);
            $currentPassword = $_POST['current_password'] ?? '';
            $newPassword = $_POST['new_password'] ?? '';
            
            if (!empty($username) && !empty($email)) {
                try {
                    $database = new Database();
                    $db = $database->getConnection();
                    
                    // Verify current password if new password is provided
                    if (!empty($newPassword)) {
                        $stmt = $db->prepare("SELECT password_hash FROM admins WHERE id = ?");
                        $stmt->execute([$_SESSION['admin_id']]);
                        $admin = $stmt->fetch();
                        
                        if (!verifyPassword($currentPassword, $admin['password_hash'])) {
                            throw new Exception('Current password is incorrect');
                        }
                        
                        // Update with new password
                        $passwordHash = hashPassword($newPassword);
                        $stmt = $db->prepare("UPDATE admins SET username = ?, email = ?, password_hash = ? WHERE id = ?");
                        $stmt->execute([$username, $email, $passwordHash, $_SESSION['admin_id']]);
                    } else {
                        // Update without password change
                        $stmt = $db->prepare("UPDATE admins SET username = ?, email = ? WHERE id = ?");
                        $stmt->execute([$username, $email, $_SESSION['admin_id']]);
                    }
                    
                    $_SESSION['admin_username'] = $username;
                    $_SESSION['admin_email'] = $email;
                    
                    $message = 'Profile updated successfully!';
                    $messageType = 'success';
                    
                } catch (Exception $e) {
                    $message = 'Error updating profile: ' . $e->getMessage();
                    $messageType = 'danger';
                }
            } else {
                $message = 'Username and email are required';
                $messageType = 'danger';
            }
            break;
    }
}

// Get current admin info
$database = new Database();
$db = $database->getConnection();
$stmt = $db->prepare("SELECT * FROM admins WHERE id = ?");
$stmt->execute([$_SESSION['admin_id']]);
$admin = $stmt->fetch();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            border-radius: 8px;
            margin: 5px 10px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">Admin Panel</h4>
                        <small class="text-white-50">Weight Loss Dashboard</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.php">
                                <i class="fas fa-users me-2"></i>
                                Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-play-circle me-2"></i>
                                Courses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="analytics.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                Analytics
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="settings.php">
                                <i class="fas fa-cog me-2"></i>
                                Settings
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Settings</h1>
                </div>

                <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-lg-8">
                        <!-- Profile Settings -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Profile Settings</h6>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="">
                                    <input type="hidden" name="action" value="update_profile">
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="username" class="form-label">Username</label>
                                                <input type="text" class="form-control" id="username" name="username" 
                                                       value="<?php echo htmlspecialchars($admin['username']); ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="email" class="form-label">Email</label>
                                                <input type="email" class="form-control" id="email" name="email" 
                                                       value="<?php echo htmlspecialchars($admin['email']); ?>" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <hr>
                                    <h6>Change Password (Optional)</h6>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="current_password" class="form-label">Current Password</label>
                                                <input type="password" class="form-control" id="current_password" name="current_password">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="new_password" class="form-label">New Password</label>
                                                <input type="password" class="form-control" id="new_password" name="new_password">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary">Update Profile</button>
                                </form>
                            </div>
                        </div>

                        <!-- System Information -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">System Information</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Application Version:</strong> <?php echo APP_VERSION; ?></p>
                                        <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
                                        <p><strong>Database:</strong> MySQL</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Environment:</strong> <?php echo $_ENV['APP_ENV'] ?? 'development'; ?></p>
                                        <p><strong>Timezone:</strong> <?php echo date_default_timezone_get(); ?></p>
                                        <p><strong>Last Login:</strong> <?php echo date('M j, Y g:i A', strtotime($admin['updated_at'])); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <!-- Quick Actions -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                            </div>
                            <div class="card-body">
                                <a href="users.php" class="btn btn-outline-primary btn-block mb-2 d-block">
                                    <i class="fas fa-users me-2"></i>
                                    Manage Users
                                </a>
                                <a href="courses.php" class="btn btn-outline-success btn-block mb-2 d-block">
                                    <i class="fas fa-play-circle me-2"></i>
                                    Manage Courses
                                </a>
                                <a href="analytics.php" class="btn btn-outline-info btn-block mb-2 d-block">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    View Analytics
                                </a>
                                <hr>
                                <a href="logout.php" class="btn btn-outline-danger btn-block d-block">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    Logout
                                </a>
                            </div>
                        </div>

                        <!-- Environment Info -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Environment</h6>
                            </div>
                            <div class="card-body">
                                <div class="text-center">
                                    <?php if (($_ENV['APP_ENV'] ?? 'development') === 'development'): ?>
                                    <span class="badge bg-warning text-dark">Development Mode</span>
                                    <?php else: ?>
                                    <span class="badge bg-success">Production Mode</span>
                                    <?php endif; ?>
                                </div>
                                <hr>
                                <small class="text-muted">
                                    <strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?><br>
                                    <strong>Host:</strong> <?php echo $_SERVER['HTTP_HOST'] ?? 'localhost'; ?><br>
                                    <strong>Port:</strong> <?php echo $_SERVER['SERVER_PORT'] ?? '80'; ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
