<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/Auth.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$database = new Database();
$db = $database->getConnection();

// Fetch comprehensive statistics
$stats = [];

// User Statistics
$stmt = $db->query("
    SELECT 
        COUNT(*) as total_users,
        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_users,
        SUM(CASE WHEN last_login IS NOT NULL THEN 1 ELSE 0 END) as users_with_login,
        SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as new_users_week,
        SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as new_users_month
    FROM users
");
$userStats = $stmt->fetch();

// Weight Loss Statistics
$stmt = $db->query("
    SELECT 
        COUNT(CASE WHEN weight IS NOT NULL AND target_weight IS NOT NULL THEN 1 END) as users_with_goals,
        AVG(CASE WHEN weight IS NOT NULL AND height IS NOT NULL AND height > 0 
            THEN weight / POWER(height/100, 2) ELSE NULL END) as avg_bmi,
        AVG(CASE WHEN weight IS NOT NULL AND target_weight IS NOT NULL 
            THEN weight - target_weight ELSE NULL END) as avg_weight_to_lose,
        COUNT(CASE WHEN weight IS NOT NULL AND target_weight IS NOT NULL AND weight <= target_weight 
            THEN 1 END) as users_reached_goal
    FROM users
");
$weightStats = $stmt->fetch();

// Course Progress Statistics
$stmt = $db->query("
    SELECT
        COUNT(DISTINCT u.id) as users_in_courses,
        AVG(CASE WHEN vp.total_videos > 0 THEN (vp.completed_videos / vp.total_videos) * 100 ELSE 0 END) as avg_progress,
        SUM(vp.completed_videos) as total_videos_watched,
        COUNT(CASE WHEN vp.completed_videos = vp.total_videos THEN 1 END) as completed_courses
    FROM users u
    LEFT JOIN user_courses uc ON u.id = uc.user_id AND uc.is_active = 1
    LEFT JOIN (
        SELECT
            user_id,
            COUNT(*) as total_videos,
            SUM(CASE WHEN is_completed = 1 THEN 1 ELSE 0 END) as completed_videos
        FROM user_video_progress
        GROUP BY user_id
    ) vp ON u.id = vp.user_id
    WHERE uc.course_id IS NOT NULL
");
$courseStats = $stmt->fetch();

// Recent Activity
$stmt = $db->query("
    SELECT u.full_name, u.email, u.created_at as activity_time, 'new_user' as activity_type
    FROM users u
    WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)

    UNION ALL

    SELECT u.full_name, u.email, vp.last_watched_at as activity_time, 'video_completed' as activity_type
    FROM user_video_progress vp
    JOIN users u ON vp.user_id = u.id
    WHERE vp.is_completed = 1 AND vp.last_watched_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)

    ORDER BY activity_time DESC
    LIMIT 10
");
$recentActivity = $stmt->fetchAll();

// BMI Distribution
$stmt = $db->query("
    SELECT 
        SUM(CASE WHEN bmi < 18.5 THEN 1 ELSE 0 END) as underweight,
        SUM(CASE WHEN bmi >= 18.5 AND bmi < 25 THEN 1 ELSE 0 END) as normal,
        SUM(CASE WHEN bmi >= 25 AND bmi < 30 THEN 1 ELSE 0 END) as overweight,
        SUM(CASE WHEN bmi >= 30 THEN 1 ELSE 0 END) as obese
    FROM (
        SELECT 
            CASE WHEN weight IS NOT NULL AND height IS NOT NULL AND height > 0 
                THEN weight / POWER(height/100, 2) 
                ELSE NULL 
            END as bmi
        FROM users
        WHERE weight IS NOT NULL AND height IS NOT NULL AND height > 0
    ) bmi_data
");
$bmiDistribution = $stmt->fetch();

// Top Performers (users with highest progress)
$stmt = $db->query("
    SELECT
        u.full_name,
        u.email,
        u.weight,
        u.target_weight,
        u.height,
        CASE WHEN u.weight IS NOT NULL AND u.height IS NOT NULL AND u.height > 0
            THEN u.weight / POWER(u.height/100, 2)
            ELSE NULL
        END as bmi,
        CASE WHEN vp.total_videos > 0
            THEN (vp.completed_videos / vp.total_videos) * 100
            ELSE 0
        END as progress_percentage,
        vp.completed_videos,
        vp.total_videos
    FROM users u
    LEFT JOIN (
        SELECT
            user_id,
            COUNT(*) as total_videos,
            SUM(CASE WHEN is_completed = 1 THEN 1 ELSE 0 END) as completed_videos
        FROM user_video_progress
        GROUP BY user_id
    ) vp ON u.id = vp.user_id
    WHERE u.is_active = 1 AND vp.total_videos > 0
    ORDER BY progress_percentage DESC, completed_videos DESC
    LIMIT 5
");
$topPerformers = $stmt->fetchAll();

function getBMICategory($bmi) {
    if ($bmi === null) return 'Unknown';
    if ($bmi < 18.5) return 'Underweight';
    if ($bmi < 25) return 'Normal';
    if ($bmi < 30) return 'Overweight';
    return 'Obese';
}

function getBMIColor($bmi) {
    if ($bmi === null) return '#6b7280';
    if ($bmi < 18.5) return '#06b6d4';
    if ($bmi < 25) return '#10b981';
    if ($bmi < 30) return '#f59e0b';
    return '#ef4444';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weight Loss Dashboard - HomeWorkout Pro Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css" rel="stylesheet">
    <style>
        :root {
            /* Minimalistic Color Palette */
            --primary: #059669;
            --primary-light: #10b981;
            --secondary: #0891b2;
            --accent: #dc2626;
            --success: #16a34a;
            --warning: #ca8a04;
            --info: #0284c7;
            --light: #ffffff;
            --surface: #f9fafb;
            --border: #e5e7eb;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --text-muted: #9ca3af;

            /* Minimalistic Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

            /* Consistent Spacing */
            --spacing-xs: 0.5rem;
            --spacing-sm: 0.75rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --spacing-2xl: 3rem;

            /* Consistent Border Radius */
            --radius-sm: 6px;
            --radius: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 20px;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--surface);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            line-height: 1.6;
            color: var(--text-primary);
            font-size: 14px;
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .main-card {
            background: var(--light);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
            overflow: hidden;
            margin-bottom: var(--spacing-xl);
        }

        .header-section {
            background: var(--light);
            border-bottom: 1px solid var(--border);
            padding: var(--spacing-2xl) var(--spacing-xl);
            position: relative;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 700;
            margin: 0 0 var(--spacing-xs) 0;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .dashboard-title i {
            color: var(--primary);
            font-size: 1.75rem;
        }

        .dashboard-subtitle {
            font-size: 1rem;
            margin: 0;
            font-weight: 400;
            color: var(--text-secondary);
        }

        .header-actions {
            display: flex;
            gap: var(--spacing-sm);
            align-items: center;
        }

        .btn-minimal {
            background: var(--light);
            color: var(--text-primary);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: var(--spacing-sm) var(--spacing-md);
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .btn-minimal:hover {
            background: var(--surface);
            color: var(--primary);
            text-decoration: none;
            box-shadow: var(--shadow-sm);
        }

        .btn-primary {
            background: var(--primary);
            color: white;
            border: 1px solid var(--primary);
        }

        .btn-primary:hover {
            background: var(--primary-light);
            border-color: var(--primary-light);
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-lg);
            padding: var(--spacing-xl);
            background: var(--surface);
        }

        .stat-card {
            background: var(--light);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border);
            transition: all 0.2s ease;
            text-align: center;
        }

        .stat-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            margin: 0 auto var(--spacing-md) auto;
            color: white;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0 0 var(--spacing-xs) 0;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin: 0;
            font-weight: 500;
        }

        .content-section {
            padding: var(--spacing-xl);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 var(--spacing-lg) 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .section-title i {
            color: var(--primary);
            font-size: 1.125rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
        }

        .chart-container {
            background: var(--light);
            border-radius: var(--radius-md);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border);
        }

        .activity-feed {
            background: var(--light);
            border-radius: var(--radius-md);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border);
            max-height: 500px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: var(--spacing-md) 0;
            border-bottom: 1px solid var(--border);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 36px;
            height: 36px;
            border-radius: var(--radius);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--spacing-md);
            font-size: 0.875rem;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 500;
            color: var(--text-primary);
            margin: 0 0 var(--spacing-xs) 0;
            font-size: 0.875rem;
        }

        .activity-subtitle {
            color: var(--text-secondary);
            margin: 0;
            font-size: 0.75rem;
        }

        .performers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--spacing-lg);
        }

        .performer-card {
            background: var(--light);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border);
            transition: all 0.2s ease;
        }

        .performer-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .performer-header {
            display: flex;
            align-items: center;
            margin-bottom: var(--spacing-md);
        }

        .performer-avatar {
            width: 44px;
            height: 44px;
            border-radius: var(--radius);
            background: var(--primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1rem;
            margin-right: var(--spacing-md);
        }

        .performer-info {
            flex: 1;
        }

        .performer-name {
            font-weight: 500;
            color: var(--text-primary);
            margin: 0 0 var(--spacing-xs) 0;
            font-size: 0.875rem;
        }

        .performer-email {
            color: var(--text-secondary);
            margin: 0;
            font-size: 0.75rem;
        }

        .performer-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        .performer-stat {
            text-align: center;
            padding: var(--spacing-sm);
            background: var(--surface);
            border-radius: var(--radius-sm);
        }

        .performer-stat-value {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--primary);
            margin: 0;
        }

        .performer-stat-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
            margin: 0;
            font-weight: 500;
        }

        @media (max-width: 1200px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .dashboard-container {
                padding: var(--spacing-md);
            }

            .header-section {
                padding: var(--spacing-xl) var(--spacing-md);
            }

            .header-content {
                flex-direction: column;
                gap: var(--spacing-lg);
                text-align: center;
            }

            .header-actions {
                justify-content: center;
            }

            .dashboard-title {
                font-size: 1.75rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                padding: var(--spacing-md);
                gap: var(--spacing-md);
            }

            .content-section {
                padding: var(--spacing-md);
            }

            .performers-grid {
                grid-template-columns: 1fr;
            }

            .performer-stats {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }

            .header-actions {
                flex-direction: column;
                width: 100%;
            }

            .btn-minimal {
                justify-content: center;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="main-card">
            <!-- Header Section -->
            <div class="header-section">
                <div class="header-content">
                    <div>
                        <h1 class="dashboard-title">
                            <i class="bi bi-heart-pulse"></i>Weight Loss Dashboard
                        </h1>
                        <p class="dashboard-subtitle">Health & fitness analytics</p>
                    </div>
                    <div class="header-actions">
                        <a href="users_modern.php" class="btn-minimal btn-primary">
                            <i class="bi bi-people-fill"></i>
                            Users
                        </a>
                        <a href="courses.php" class="btn-minimal">
                            <i class="bi bi-play-circle-fill"></i>
                            Courses
                        </a>
                        <a href="logout.php" class="btn-minimal">
                            <i class="bi bi-box-arrow-right"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--primary);">
                        <i class="bi bi-people-fill"></i>
                    </div>
                    <div class="stat-value"><?= number_format($userStats['total_users'] ?? 0) ?></div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--success);">
                        <i class="bi bi-person-check-fill"></i>
                    </div>
                    <div class="stat-value"><?= number_format($userStats['active_users'] ?? 0) ?></div>
                    <div class="stat-label">Active Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--warning);">
                        <i class="bi bi-bullseye"></i>
                    </div>
                    <div class="stat-value"><?= number_format($weightStats['users_with_goals'] ?? 0) ?></div>
                    <div class="stat-label">Users with Goals</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--info);">
                        <i class="bi bi-speedometer2"></i>
                    </div>
                    <div class="stat-value"><?= $weightStats['avg_bmi'] ? number_format($weightStats['avg_bmi'], 1) : '--' ?></div>
                    <div class="stat-label">Average BMI</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--accent);">
                        <i class="bi bi-arrow-down-circle-fill"></i>
                    </div>
                    <div class="stat-value"><?= $weightStats['avg_weight_to_lose'] ? number_format($weightStats['avg_weight_to_lose'], 1) . ' kg' : '--' ?></div>
                    <div class="stat-label">Avg Weight to Lose</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--success);">
                        <i class="bi bi-trophy-fill"></i>
                    </div>
                    <div class="stat-value"><?= number_format($weightStats['users_reached_goal'] ?? 0) ?></div>
                    <div class="stat-label">Goals Achieved</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--secondary);">
                        <i class="bi bi-play-circle-fill"></i>
                    </div>
                    <div class="stat-value"><?= number_format($courseStats['total_videos_watched'] ?? 0) ?></div>
                    <div class="stat-label">Videos Watched</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--primary-light);">
                        <i class="bi bi-graph-up-arrow"></i>
                    </div>
                    <div class="stat-value"><?= $courseStats['avg_progress'] ? number_format($courseStats['avg_progress'], 1) . '%' : '--' ?></div>
                    <div class="stat-label">Avg Progress</div>
                </div>
            </div>

            <!-- Content Section -->
            <div class="content-section">
                <!-- Dashboard Grid -->
                <div class="dashboard-grid">
                    <!-- BMI Distribution Chart -->
                    <div class="chart-container">
                        <h3 class="section-title">
                            <i class="bi bi-pie-chart-fill me-2" style="color: var(--primary);"></i>
                            BMI Distribution
                        </h3>
                        <canvas id="bmiChart" width="400" height="200"></canvas>
                    </div>

                    <!-- Recent Activity Feed -->
                    <div class="activity-feed">
                        <h3 class="section-title">
                            <i class="bi bi-activity me-2" style="color: var(--primary);"></i>
                            Recent Activity
                        </h3>
                        <?php if (empty($recentActivity)): ?>
                        <div class="text-center py-4">
                            <i class="bi bi-clock-history display-4 text-muted"></i>
                            <p class="text-muted mt-2">No recent activity</p>
                        </div>
                        <?php else: ?>
                        <?php foreach ($recentActivity as $activity): ?>
                        <div class="activity-item">
                            <div class="activity-icon" style="background: <?= $activity['activity_type'] === 'new_user' ? 'var(--primary)' : 'var(--success)' ?>;">
                                <i class="bi bi-<?= $activity['activity_type'] === 'new_user' ? 'person-plus' : 'play-circle' ?>"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">
                                    <?= htmlspecialchars($activity['full_name']) ?>
                                    <?= $activity['activity_type'] === 'new_user' ? 'joined the platform' : 'completed a video' ?>
                                </div>
                                <div class="activity-subtitle">
                                    <?= htmlspecialchars($activity['email']) ?> •
                                    <?= date('M d, H:i', strtotime($activity['activity_time'])) ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Top Performers Section -->
                <div class="mt-4">
                    <h3 class="section-title">
                        <i class="bi bi-star-fill me-2" style="color: var(--primary);"></i>
                        Top Performers
                    </h3>
                    <div class="performers-grid">
                        <?php if (empty($topPerformers)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-trophy display-1 text-muted"></i>
                            <h4 class="mt-3 text-muted">No Active Users</h4>
                            <p class="text-muted">Users will appear here once they start their fitness journey.</p>
                        </div>
                        <?php else: ?>
                        <?php foreach ($topPerformers as $performer): ?>
                        <div class="performer-card">
                            <div class="performer-header">
                                <div class="performer-avatar">
                                    <?= strtoupper(substr($performer['full_name'] ?? 'U', 0, 2)) ?>
                                </div>
                                <div class="performer-info">
                                    <div class="performer-name"><?= htmlspecialchars($performer['full_name']) ?></div>
                                    <div class="performer-email"><?= htmlspecialchars($performer['email']) ?></div>
                                </div>
                            </div>
                            <div class="performer-stats">
                                <div class="performer-stat">
                                    <div class="performer-stat-value"><?= number_format($performer['progress_percentage'] ?? 0, 0) ?>%</div>
                                    <div class="performer-stat-label">Progress</div>
                                </div>
                                <div class="performer-stat">
                                    <div class="performer-stat-value"><?= ($performer['completed_videos'] ?? 0) ?>/<?= ($performer['total_videos'] ?? 0) ?></div>
                                    <div class="performer-stat-label">Videos</div>
                                </div>
                                <div class="performer-stat">
                                    <div class="performer-stat-value" style="color: <?= getBMIColor($performer['bmi']) ?>;">
                                        <?= $performer['bmi'] ? number_format($performer['bmi'], 1) : '--' ?>
                                    </div>
                                    <div class="performer-stat-label">BMI</div>
                                </div>
                                <div class="performer-stat">
                                    <div class="performer-stat-value" style="color: var(--danger);">
                                        <?php if ($performer['weight'] && $performer['target_weight']): ?>
                                            <?= number_format($performer['weight'] - $performer['target_weight'], 1) ?> kg
                                        <?php else: ?>
                                            --
                                        <?php endif; ?>
                                    </div>
                                    <div class="performer-stat-label">To Lose</div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script>
        // BMI Distribution Chart
        const bmiCtx = document.getElementById('bmiChart').getContext('2d');
        const bmiChart = new Chart(bmiCtx, {
            type: 'doughnut',
            data: {
                labels: ['Underweight', 'Normal', 'Overweight', 'Obese'],
                datasets: [{
                    data: [
                        <?= $bmiDistribution['underweight'] ?? 0 ?>,
                        <?= $bmiDistribution['normal'] ?? 0 ?>,
                        <?= $bmiDistribution['overweight'] ?? 0 ?>,
                        <?= $bmiDistribution['obese'] ?? 0 ?>
                    ],
                    backgroundColor: [
                        '#06b6d4',
                        '#10b981',
                        '#f59e0b',
                        '#ef4444'
                    ],
                    borderWidth: 0,
                    hoverOffset: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                family: 'Inter',
                                size: 12,
                                weight: '500'
                            }
                        }
                    }
                }
            }
        });

        // Auto-refresh data every 30 seconds
        setInterval(() => {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
