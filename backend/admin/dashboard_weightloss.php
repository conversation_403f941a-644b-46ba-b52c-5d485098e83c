<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/Auth.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$database = new Database();
$db = $database->getConnection();

// Fetch comprehensive statistics
$stats = [];

// User Statistics
$stmt = $db->query("
    SELECT 
        COUNT(*) as total_users,
        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_users,
        SUM(CASE WHEN last_login IS NOT NULL THEN 1 ELSE 0 END) as users_with_login,
        SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as new_users_week,
        SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as new_users_month
    FROM users
");
$userStats = $stmt->fetch();

// Weight Loss Statistics
$stmt = $db->query("
    SELECT 
        COUNT(CASE WHEN weight IS NOT NULL AND target_weight IS NOT NULL THEN 1 END) as users_with_goals,
        AVG(CASE WHEN weight IS NOT NULL AND height IS NOT NULL AND height > 0 
            THEN weight / POWER(height/100, 2) ELSE NULL END) as avg_bmi,
        AVG(CASE WHEN weight IS NOT NULL AND target_weight IS NOT NULL 
            THEN weight - target_weight ELSE NULL END) as avg_weight_to_lose,
        COUNT(CASE WHEN weight IS NOT NULL AND target_weight IS NOT NULL AND weight <= target_weight 
            THEN 1 END) as users_reached_goal
    FROM users
");
$weightStats = $stmt->fetch();

// Course Progress Statistics
$stmt = $db->query("
    SELECT 
        COUNT(DISTINCT u.id) as users_in_courses,
        AVG(CASE WHEN vp.total_videos > 0 THEN (vp.completed_videos / vp.total_videos) * 100 ELSE 0 END) as avg_progress,
        SUM(vp.completed_videos) as total_videos_watched,
        COUNT(CASE WHEN vp.completed_videos = vp.total_videos THEN 1 END) as completed_courses
    FROM users u
    LEFT JOIN (
        SELECT 
            user_id,
            COUNT(*) as total_videos,
            SUM(CASE WHEN is_completed = 1 THEN 1 ELSE 0 END) as completed_videos
        FROM video_progress 
        GROUP BY user_id
    ) vp ON u.id = vp.user_id
    WHERE u.course_id IS NOT NULL
");
$courseStats = $stmt->fetch();

// Recent Activity
$stmt = $db->query("
    SELECT u.full_name, u.email, u.created_at, 'new_user' as activity_type
    FROM users u 
    WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    
    UNION ALL
    
    SELECT u.full_name, u.email, vp.last_watched_at, 'video_completed' as activity_type
    FROM video_progress vp
    JOIN users u ON vp.user_id = u.id
    WHERE vp.is_completed = 1 AND vp.last_watched_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    
    ORDER BY created_at DESC
    LIMIT 10
");
$recentActivity = $stmt->fetchAll();

// BMI Distribution
$stmt = $db->query("
    SELECT 
        SUM(CASE WHEN bmi < 18.5 THEN 1 ELSE 0 END) as underweight,
        SUM(CASE WHEN bmi >= 18.5 AND bmi < 25 THEN 1 ELSE 0 END) as normal,
        SUM(CASE WHEN bmi >= 25 AND bmi < 30 THEN 1 ELSE 0 END) as overweight,
        SUM(CASE WHEN bmi >= 30 THEN 1 ELSE 0 END) as obese
    FROM (
        SELECT 
            CASE WHEN weight IS NOT NULL AND height IS NOT NULL AND height > 0 
                THEN weight / POWER(height/100, 2) 
                ELSE NULL 
            END as bmi
        FROM users
        WHERE weight IS NOT NULL AND height IS NOT NULL AND height > 0
    ) bmi_data
");
$bmiDistribution = $stmt->fetch();

// Top Performers (users with highest progress)
$stmt = $db->query("
    SELECT 
        u.full_name,
        u.email,
        u.weight,
        u.target_weight,
        u.height,
        CASE WHEN u.weight IS NOT NULL AND u.height IS NOT NULL AND u.height > 0 
            THEN u.weight / POWER(u.height/100, 2) 
            ELSE NULL 
        END as bmi,
        CASE WHEN vp.total_videos > 0 
            THEN (vp.completed_videos / vp.total_videos) * 100 
            ELSE 0 
        END as progress_percentage,
        vp.completed_videos,
        vp.total_videos
    FROM users u
    LEFT JOIN (
        SELECT 
            user_id,
            COUNT(*) as total_videos,
            SUM(CASE WHEN is_completed = 1 THEN 1 ELSE 0 END) as completed_videos
        FROM video_progress 
        GROUP BY user_id
    ) vp ON u.id = vp.user_id
    WHERE u.is_active = 1 AND vp.total_videos > 0
    ORDER BY progress_percentage DESC, completed_videos DESC
    LIMIT 5
");
$topPerformers = $stmt->fetchAll();

function getBMICategory($bmi) {
    if ($bmi === null) return 'Unknown';
    if ($bmi < 18.5) return 'Underweight';
    if ($bmi < 25) return 'Normal';
    if ($bmi < 30) return 'Overweight';
    return 'Obese';
}

function getBMIColor($bmi) {
    if ($bmi === null) return '#6b7280';
    if ($bmi < 18.5) return '#06b6d4';
    if ($bmi < 25) return '#10b981';
    if ($bmi < 30) return '#f59e0b';
    return '#ef4444';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weight Loss Dashboard - HomeWorkout Pro Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #10b981;
            --primary-dark: #059669;
            --secondary: #06b6d4;
            --accent: #f59e0b;
            --success: #22c55e;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #06b6d4;
            --light: #f8fafc;
            --dark: #1e293b;
            --border: #e2e8f0;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            --gradient-success: linear-gradient(135deg, var(--success) 0%, var(--primary) 100%);
            --gradient-warning: linear-gradient(135deg, var(--warning) 0%, #fb923c 100%);
            --gradient-surface: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            line-height: 1.6;
            color: var(--dark);
        }

        .dashboard-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
        }

        .main-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .header-section {
            background: var(--gradient-primary);
            color: white;
            padding: 3rem 2rem;
            position: relative;
            overflow: hidden;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            opacity: 0.6;
        }

        .header-content {
            position: relative;
            z-index: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dashboard-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin: 0 0 0.5rem 0;
            background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .dashboard-subtitle {
            font-size: 1.125rem;
            opacity: 0.9;
            margin: 0;
            font-weight: 400;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn-premium {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 0.875rem 1.5rem;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            backdrop-filter: blur(10px);
        }

        .btn-premium:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin: -1rem 2rem 2rem 2rem;
            position: relative;
            z-index: 2;
        }

        .stat-card {
            background: var(--gradient-surface);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-lg);
        }

        .stat-card:hover::before {
            transform: scaleX(1);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: white;
            box-shadow: 0 8px 16px rgba(16, 185, 129, 0.3);
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--dark);
            margin: 0;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #64748b;
            margin: 0.5rem 0 0 0;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .content-section {
            padding: 2rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark);
            margin: 0 0 1.5rem 0;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .chart-container {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
        }

        .activity-feed {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
            max-height: 500px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid var(--border);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1rem;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: var(--dark);
            margin: 0 0 0.25rem 0;
            font-size: 0.875rem;
        }

        .activity-subtitle {
            color: #64748b;
            margin: 0;
            font-size: 0.75rem;
        }

        .performers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .performer-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
            transition: all 0.3s ease;
        }

        .performer-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .performer-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .performer-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: var(--gradient-primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.125rem;
            margin-right: 1rem;
        }

        .performer-info {
            flex: 1;
        }

        .performer-name {
            font-weight: 600;
            color: var(--dark);
            margin: 0 0 0.25rem 0;
            font-size: 0.875rem;
        }

        .performer-email {
            color: #64748b;
            margin: 0;
            font-size: 0.75rem;
        }

        .performer-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-top: 1rem;
        }

        .performer-stat {
            text-align: center;
        }

        .performer-stat-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary);
            margin: 0;
        }

        .performer-stat-label {
            font-size: 0.75rem;
            color: #64748b;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        @media (max-width: 1200px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .dashboard-container {
                padding: 1rem;
            }
            
            .header-section {
                padding: 2rem 1rem;
            }
            
            .header-content {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            
            .dashboard-title {
                font-size: 2rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                margin: -1rem 1rem 2rem 1rem;
            }
            
            .content-section {
                padding: 1rem;
            }
            
            .performers-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="main-card">
            <!-- Header Section -->
            <div class="header-section">
                <div class="header-content">
                    <div>
                        <h1 class="dashboard-title">
                            <i class="bi bi-heart-pulse me-3"></i>Weight Loss Dashboard
                        </h1>
                        <p class="dashboard-subtitle">HomeWorkout Pro - Transform Lives Through Fitness</p>
                    </div>
                    <div class="header-actions">
                        <a href="users_modern.php" class="btn-premium">
                            <i class="bi bi-people-fill"></i>
                            Manage Users
                        </a>
                        <a href="courses.php" class="btn-premium">
                            <i class="bi bi-play-circle-fill"></i>
                            Courses
                        </a>
                        <a href="logout.php" class="btn-premium">
                            <i class="bi bi-box-arrow-right"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--gradient-primary);">
                        <i class="bi bi-people-fill"></i>
                    </div>
                    <div class="stat-value"><?= number_format($userStats['total_users']) ?></div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--gradient-success);">
                        <i class="bi bi-person-check-fill"></i>
                    </div>
                    <div class="stat-value"><?= number_format($userStats['active_users']) ?></div>
                    <div class="stat-label">Active Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--gradient-warning);">
                        <i class="bi bi-bullseye"></i>
                    </div>
                    <div class="stat-value"><?= number_format($weightStats['users_with_goals']) ?></div>
                    <div class="stat-label">Users with Goals</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, var(--info), #0891b2);">
                        <i class="bi bi-speedometer2"></i>
                    </div>
                    <div class="stat-value"><?= $weightStats['avg_bmi'] ? number_format($weightStats['avg_bmi'], 1) : '--' ?></div>
                    <div class="stat-label">Average BMI</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, var(--danger), #dc2626);">
                        <i class="bi bi-arrow-down-circle-fill"></i>
                    </div>
                    <div class="stat-value"><?= $weightStats['avg_weight_to_lose'] ? number_format($weightStats['avg_weight_to_lose'], 1) . ' kg' : '--' ?></div>
                    <div class="stat-label">Avg Weight to Lose</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, var(--success), #16a34a);">
                        <i class="bi bi-trophy-fill"></i>
                    </div>
                    <div class="stat-value"><?= number_format($weightStats['users_reached_goal']) ?></div>
                    <div class="stat-label">Goals Achieved</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, var(--secondary), #0284c7);">
                        <i class="bi bi-play-circle-fill"></i>
                    </div>
                    <div class="stat-value"><?= number_format($courseStats['total_videos_watched']) ?></div>
                    <div class="stat-label">Videos Watched</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, var(--accent), #d97706);">
                        <i class="bi bi-graph-up-arrow"></i>
                    </div>
                    <div class="stat-value"><?= $courseStats['avg_progress'] ? number_format($courseStats['avg_progress'], 1) . '%' : '--' ?></div>
                    <div class="stat-label">Avg Progress</div>
                </div>
            </div>

            <!-- Content Section -->
            <div class="content-section">
                <!-- Dashboard Grid -->
                <div class="dashboard-grid">
                    <!-- BMI Distribution Chart -->
                    <div class="chart-container">
                        <h3 class="section-title">
                            <i class="bi bi-pie-chart-fill me-2" style="color: var(--primary);"></i>
                            BMI Distribution
                        </h3>
                        <canvas id="bmiChart" width="400" height="200"></canvas>
                    </div>

                    <!-- Recent Activity Feed -->
                    <div class="activity-feed">
                        <h3 class="section-title">
                            <i class="bi bi-activity me-2" style="color: var(--primary);"></i>
                            Recent Activity
                        </h3>
                        <?php if (empty($recentActivity)): ?>
                        <div class="text-center py-4">
                            <i class="bi bi-clock-history display-4 text-muted"></i>
                            <p class="text-muted mt-2">No recent activity</p>
                        </div>
                        <?php else: ?>
                        <?php foreach ($recentActivity as $activity): ?>
                        <div class="activity-item">
                            <div class="activity-icon" style="background: <?= $activity['activity_type'] === 'new_user' ? 'var(--gradient-primary)' : 'var(--gradient-success)' ?>;">
                                <i class="bi bi-<?= $activity['activity_type'] === 'new_user' ? 'person-plus' : 'play-circle' ?>"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">
                                    <?= htmlspecialchars($activity['full_name']) ?>
                                    <?= $activity['activity_type'] === 'new_user' ? 'joined the platform' : 'completed a video' ?>
                                </div>
                                <div class="activity-subtitle">
                                    <?= htmlspecialchars($activity['email']) ?> •
                                    <?= date('M d, H:i', strtotime($activity['created_at'])) ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Top Performers Section -->
                <div class="mt-4">
                    <h3 class="section-title">
                        <i class="bi bi-star-fill me-2" style="color: var(--primary);"></i>
                        Top Performers
                    </h3>
                    <div class="performers-grid">
                        <?php if (empty($topPerformers)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-trophy display-1 text-muted"></i>
                            <h4 class="mt-3 text-muted">No Active Users</h4>
                            <p class="text-muted">Users will appear here once they start their fitness journey.</p>
                        </div>
                        <?php else: ?>
                        <?php foreach ($topPerformers as $performer): ?>
                        <div class="performer-card">
                            <div class="performer-header">
                                <div class="performer-avatar">
                                    <?= strtoupper(substr($performer['full_name'] ?? 'U', 0, 2)) ?>
                                </div>
                                <div class="performer-info">
                                    <div class="performer-name"><?= htmlspecialchars($performer['full_name']) ?></div>
                                    <div class="performer-email"><?= htmlspecialchars($performer['email']) ?></div>
                                </div>
                            </div>
                            <div class="performer-stats">
                                <div class="performer-stat">
                                    <div class="performer-stat-value"><?= number_format($performer['progress_percentage'], 0) ?>%</div>
                                    <div class="performer-stat-label">Progress</div>
                                </div>
                                <div class="performer-stat">
                                    <div class="performer-stat-value"><?= $performer['completed_videos'] ?>/<?= $performer['total_videos'] ?></div>
                                    <div class="performer-stat-label">Videos</div>
                                </div>
                                <div class="performer-stat">
                                    <div class="performer-stat-value" style="color: <?= getBMIColor($performer['bmi']) ?>;">
                                        <?= $performer['bmi'] ? number_format($performer['bmi'], 1) : '--' ?>
                                    </div>
                                    <div class="performer-stat-label">BMI</div>
                                </div>
                                <div class="performer-stat">
                                    <div class="performer-stat-value" style="color: var(--danger);">
                                        <?php if ($performer['weight'] && $performer['target_weight']): ?>
                                            <?= number_format($performer['weight'] - $performer['target_weight'], 1) ?> kg
                                        <?php else: ?>
                                            --
                                        <?php endif; ?>
                                    </div>
                                    <div class="performer-stat-label">To Lose</div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script>
        // BMI Distribution Chart
        const bmiCtx = document.getElementById('bmiChart').getContext('2d');
        const bmiChart = new Chart(bmiCtx, {
            type: 'doughnut',
            data: {
                labels: ['Underweight', 'Normal', 'Overweight', 'Obese'],
                datasets: [{
                    data: [
                        <?= $bmiDistribution['underweight'] ?? 0 ?>,
                        <?= $bmiDistribution['normal'] ?? 0 ?>,
                        <?= $bmiDistribution['overweight'] ?? 0 ?>,
                        <?= $bmiDistribution['obese'] ?? 0 ?>
                    ],
                    backgroundColor: [
                        '#06b6d4',
                        '#10b981',
                        '#f59e0b',
                        '#ef4444'
                    ],
                    borderWidth: 0,
                    hoverOffset: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                family: 'Inter',
                                size: 12,
                                weight: '500'
                            }
                        }
                    }
                }
            }
        });

        // Auto-refresh data every 30 seconds
        setInterval(() => {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
