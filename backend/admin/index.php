<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/Auth.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$auth = new Auth();
$database = new Database();
$db = $database->getConnection();

// Get dashboard statistics
$stats = [];

// Total users
$stmt = $db->query("SELECT COUNT(*) as total FROM users WHERE is_active = 1");
$stats['total_users'] = $stmt->fetch()['total'];

// Total courses
$stmt = $db->query("SELECT COUNT(*) as total FROM courses WHERE is_active = 1");
$stats['total_courses'] = $stmt->fetch()['total'];

// Active sessions today
$stmt = $db->query("SELECT COUNT(*) as total FROM user_sessions WHERE DATE(started_at) = CURDATE()");
$stats['active_sessions_today'] = $stmt->fetch()['total'];

// Total watch time today (in minutes)
$stmt = $db->query("
    SELECT COALESCE(SUM(duration_seconds), 0) / 60 as total_minutes 
    FROM user_activity_log 
    WHERE activity_type = 'video_complete' AND DATE(created_at) = CURDATE()
");
$stats['watch_time_today'] = round($stmt->fetch()['total_minutes'], 2);

// Recent user activity
$stmt = $db->query("
    SELECT u.full_name, u.email, ual.activity_type, ual.created_at, cv.title as video_title
    FROM user_activity_log ual
    JOIN users u ON ual.user_id = u.id
    LEFT JOIN course_videos cv ON ual.video_id = cv.id
    ORDER BY ual.created_at DESC
    LIMIT 10
");
$recent_activity = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Weight Loss Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f8fafc;
            color: #1e293b;
        }
        .sidebar {
            width: 260px;
            height: 100vh;
            background: #fff;
            border-right: 1px solid #e2e8f0;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1040;
            transition: transform 0.3s ease;
        }
        .sidebar .sidebar-header {
            padding: 2rem 1.5rem 1.5rem;
            border-bottom: 1px solid #e2e8f0;
        }
        .sidebar .sidebar-brand {
            font-size: 1.25rem;
            font-weight: 700;
            color: #6366f1;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        .sidebar .sidebar-brand i {
            width: 32px;
            height: 32px;
            background: #6366f1;
            color: #fff;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
        }
        .sidebar .nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1.5rem;
            color: #64748b;
            text-decoration: none;
            font-weight: 500;
            border: none;
            background: none;
            transition: all 0.2s;
        }
        .sidebar .nav-link.active, .sidebar .nav-link:hover {
            background: #f1f5f9;
            color: #6366f1;
        }
        .sidebar .nav-link i {
            width: 20px;
            font-size: 1.1rem;
        }
        .sidebar .nav-section-title {
            font-size: 0.75rem;
            font-weight: 600;
            color: #94a3b8;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 0 1.5rem;
            margin-bottom: 0.5rem;
        }
        .main-content {
            margin-left: 260px;
            min-height: 100vh;
            transition: margin-left 0.3s ease;
        }
        .topbar {
            background: #fff;
            border-bottom: 1px solid #e2e8f0;
            padding: 1rem 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 1030;
        }
        .topbar .menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #6366f1;
        }
        .topbar .admin-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #6366f1;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.1rem;
        }
        .dashboard-cards {
            gap: 1.5rem;
            flex-wrap: nowrap;
        }
        .icon-circle {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            font-size: 1.5rem;
        }
        .stat-card {
            background: #fff;
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.04);
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 0;
        }
        .stat-card .stat-label {
            font-size: 0.95rem;
            color: #64748b;
            font-weight: 400;
            letter-spacing: 0.01em;
        }
        .stat-card .stat-value {
            font-size: 2.1rem;
            font-weight: 700;
            color: #1e293b;
            line-height: 1.1;
        }
        .table-responsive {
            border-radius: 16px;
            overflow: hidden;
            background: #fff;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.04);
        }
        .overlay {
            display: none;
            position: fixed;
            top: 0; left: 0; right: 0; bottom: 0;
            background: rgba(30, 41, 59, 0.4);
            z-index: 1035;
        }
        @media (max-width: 991.98px) {
            .sidebar {
                transform: translateX(-100%);
            }
            .sidebar.active {
                transform: translateX(0);
            }
            .main-content {
                margin-left: 0;
            }
            .topbar .menu-btn {
                display: block;
            }
            .overlay.active {
                display: block;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="#" class="sidebar-brand"><i class="bi bi-bar-chart"></i> Admin Panel</a>
        </div>
        <div class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">Main</div>
                <a href="index.php" class="nav-link active"><i class="bi bi-house"></i> Dashboard</a>
                <a href="users.php" class="nav-link"><i class="bi bi-people"></i> Users</a>
                <a href="courses.php" class="nav-link"><i class="bi bi-journal-text"></i> Courses</a>
                <a href="analytics.php" class="nav-link"><i class="bi bi-graph-up"></i> Analytics</a>
                <a href="settings.php" class="nav-link"><i class="bi bi-gear"></i> Settings</a>
            </div>
            <div class="nav-section">
                <div class="nav-section-title">Account</div>
                <a href="logout.php" class="nav-link"><i class="bi bi-box-arrow-right"></i> Logout</a>
            </div>
        </div>
    </div>
    <div class="overlay" id="overlay"></div>
    <div class="main-content">
        <div class="topbar">
            <button class="menu-btn" id="menuBtn"><i class="bi bi-list"></i></button>
            <span class="page-title fw-bold fs-4">Dashboard</span>
            <div class="admin-avatar" title="Admin">
                <?php echo strtoupper(substr($_SESSION['admin_username'] ?? 'A', 0, 2)); ?>
            </div>
        </div>
        <div class="container-fluid py-4">
            <div class="row dashboard-cards mb-4 g-3 flex-nowrap overflow-auto">
                <div class="col-6 col-md-3">
                    <div class="stat-card text-center p-3 h-100">
                        <div class="icon-circle mb-2 bg-primary bg-opacity-10 d-inline-flex align-items-center justify-content-center">
                            <i class="bi bi-people text-primary"></i>
                        </div>
                        <div class="stat-value mb-1"><?php echo $stats['total_users']; ?></div>
                        <div class="stat-label">Total Users</div>
                    </div>
                </div>
                <div class="col-6 col-md-3">
                    <div class="stat-card text-center p-3 h-100">
                        <div class="icon-circle mb-2 bg-success bg-opacity-10 d-inline-flex align-items-center justify-content-center">
                            <i class="bi bi-journal-text text-success"></i>
                        </div>
                        <div class="stat-value mb-1"><?php echo $stats['total_courses']; ?></div>
                        <div class="stat-label">Total Courses</div>
                    </div>
                </div>
                <div class="col-6 col-md-3">
                    <div class="stat-card text-center p-3 h-100">
                        <div class="icon-circle mb-2 bg-warning bg-opacity-10 d-inline-flex align-items-center justify-content-center">
                            <i class="bi bi-lightning-charge text-warning"></i>
                        </div>
                        <div class="stat-value mb-1"><?php echo $stats['active_sessions_today']; ?></div>
                        <div class="stat-label">Active Sessions Today</div>
                    </div>
                </div>
                <div class="col-6 col-md-3">
                    <div class="stat-card text-center p-3 h-100">
                        <div class="icon-circle mb-2 bg-info bg-opacity-10 d-inline-flex align-items-center justify-content-center">
                            <i class="bi bi-clock-history text-info"></i>
                        </div>
                        <div class="stat-value mb-1"><?php echo $stats['watch_time_today']; ?></div>
                        <div class="stat-label">Watch Time Today (min)</div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="mb-3 d-flex align-items-center justify-content-between">
                        <h5 class="fw-bold mb-0">Recent User Activity</h5>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Activity</th>
                                    <th>Video</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_activity as $activity): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($activity['full_name']); ?></td>
                                        <td><?php echo htmlspecialchars($activity['email']); ?></td>
                                        <td><?php echo htmlspecialchars($activity['activity_type']); ?></td>
                                        <td><?php echo htmlspecialchars($activity['video_title'] ?? '-'); ?></td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($activity['created_at'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        const menuBtn = document.getElementById('menuBtn');
        menuBtn.addEventListener('click', function() {
            sidebar.classList.toggle('active');
            overlay.classList.toggle('active');
        });
        overlay.addEventListener('click', function() {
            sidebar.classList.remove('active');
            overlay.classList.remove('active');
        });
    </script>
</body>
</html>
