<?php
/**
 * Additional Functions (avoiding redeclaration of functions in config.php)
 */

/**
 * Validate required fields in input
 */
if (!function_exists('validateRequired')) {
    function validateRequired($input, $required) {
        foreach ($required as $field) {
            if (!isset($input[$field]) || empty($input[$field])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => "Missing required field: $field"
                ]);
                exit;
            }
        }
    }
}

/**
 * Send JSON response
 */
if (!function_exists('sendJsonResponse')) {
    function sendJsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

/**
 * Handle CORS
 */
if (!function_exists('handleCORS')) {
    function handleCORS() {
        header('Access-Control-Allow-Origin: http://localhost:3000');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-Device-Fingerprint');

        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit;
        }
    }
}
?>
