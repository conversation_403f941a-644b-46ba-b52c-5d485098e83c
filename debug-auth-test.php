<?php
/**
 * Debug Authentication Test
 */

require_once 'backend/config/config.php';
require_once 'backend/config/database.php';
require_once 'backend/includes/Auth.php';

try {
    $auth = new Auth();
    $database = new Database();
    $db = $database->getConnection();
    
    // Get test user
    $stmt = $db->query("SELECT * FROM users WHERE email = '<EMAIL>' LIMIT 1");
    $user = $stmt->fetch();
    
    if (!$user) {
        echo "❌ No test user found\n";
        exit(1);
    }
    
    // Generate authentication link
    $authLink = $auth->generateAuthLink($user['id'], 1);
    
    if ($authLink) {
        echo "🔗 Debug Auth Link: $authLink\n\n";
        echo "🔧 Instructions:\n";
        echo "1. Open browser developer tools (F12)\n";
        echo "2. Go to Console tab\n";
        echo "3. Click the link above\n";
        echo "4. Watch for debug messages in console\n";
        echo "5. Check backend server logs for API requests\n\n";
        echo "Expected debug messages:\n";
        echo "- 🚀 AuthService._initializeAuth: Starting initialization...\n";
        echo "- 🔍 AuthService._initializeAuth: URL token check - Found token\n";
        echo "- 🎫 AuthService._initializeAuth: Processing URL token...\n";
        echo "- 📡 AuthService._initializeAuth: Calling login with URL token...\n";
        echo "- 🌐 ApiService.login: Starting API login request...\n";
        echo "- 📡 ApiService.login: Making POST request to /auth/login...\n";
        echo "- 📨 ApiService.login: Response received: 200\n";
        echo "- ✅ ApiService.login: Login successful\n";
        echo "- 🎉 AuthService.login: Login completed successfully!\n";
    } else {
        echo "❌ Failed to generate authentication link\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
