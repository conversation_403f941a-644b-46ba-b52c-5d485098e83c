/// Web-specific implementation
import 'dart:html' as html;

String? getUrlTokenImpl() {
  try {
    final uri = Uri.parse(html.window.location.href);
    final token = uri.queryParameters['token'];
    print('🔍 WEB_UTILS: Current URL: ${html.window.location.href}');
    print('🔍 WEB_UTILS: Token found: ${token != null ? "YES (${token.length} chars)" : "NO"}');
    if (token != null) {
      print('🎫 WEB_UTILS: Token preview: ${token.substring(0, 20)}...');
    }
    return token;
  } catch (e) {
    print('❌ WEB_UTILS: Error getting URL token: $e');
    return null;
  }
}

void clearUrlTokenImpl() {
  try {
    final uri = Uri.parse(html.window.location.href);
    final newUri = uri.replace(queryParameters: {});
    html.window.history.replaceState(null, '', newUri.toString());
  } catch (e) {
    // Silently handle errors
  }
}
