/// Web-specific implementation
import 'dart:html' as html;

String? getUrlTokenImpl() {
  try {
    final uri = Uri.parse(html.window.location.href);
    return uri.queryParameters['token'];
  } catch (e) {
    return null;
  }
}

void clearUrlTokenImpl() {
  try {
    final uri = Uri.parse(html.window.location.href);
    final newUri = uri.replace(queryParameters: {});
    html.window.history.replaceState(null, '', newUri.toString());
  } catch (e) {
    // Silently handle errors
  }
}
