import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';

class AuthService extends ChangeNotifier {
  User? _user;
  bool _isAuthenticated = false;
  bool _isLoading = true;
  String? _error;

  // Getters
  User? get user => _user;
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;
  String? get error => _error;

  final ApiService _apiService = ApiService();

  AuthService() {
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    _setLoading(true);
    
    try {
      // Check if user data exists in storage
      final storedUser = StorageService.getUser();
      final token = StorageService.getAuthToken();
      
      if (storedUser != null && token != null) {
        // Validate token with server
        final isValid = await _apiService.validateToken();
        
        if (isValid) {
          _user = storedUser;
          _isAuthenticated = true;
          _error = null;
          
          // Refresh user data in background
          _refreshUserData();
        } else {
          // Token is invalid, clear stored data
          await _clearAuthData();
        }
      }
    } catch (e) {
      _error = e.toString();
      await _clearAuthData();
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> login(String token) async {
    _setLoading(true);
    _error = null;
    
    try {
      final response = await _apiService.login(token);
      
      if (response['success']) {
        _user = User.fromJson(response['data']['user']);
        _isAuthenticated = true;
        _error = null;
        
        notifyListeners();
        return true;
      } else {
        _error = response['message'] ?? 'Login failed';
        return false;
      }
    } catch (e) {
      _error = e.toString();
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> logout() async {
    _setLoading(true);
    
    try {
      await _clearAuthData();
    } finally {
      _setLoading(false);
    }
  }

  Future<void> checkAuthStatus() async {
    if (!_isLoading) {
      _setLoading(true);
    }
    
    try {
      final token = StorageService.getAuthToken();
      
      if (token != null) {
        final isValid = await _apiService.validateToken();
        
        if (isValid) {
          final storedUser = StorageService.getUser();
          if (storedUser != null) {
            _user = storedUser;
            _isAuthenticated = true;
            _error = null;
            
            // Refresh user data
            await _refreshUserData();
          } else {
            await _clearAuthData();
          }
        } else {
          await _clearAuthData();
        }
      } else {
        await _clearAuthData();
      }
    } catch (e) {
      _error = e.toString();
      await _clearAuthData();
    } finally {
      _setLoading(false);
    }
  }

  Future<void> refreshUserProfile() async {
    if (!_isAuthenticated) return;
    
    try {
      final updatedUser = await _apiService.getUserProfile();
      _user = updatedUser;
      _error = null;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  Future<void> _refreshUserData() async {
    try {
      final updatedUser = await _apiService.getUserProfile();
      _user = updatedUser;
      notifyListeners();
    } catch (e) {
      // Don't update error state for background refresh failures
      print('Failed to refresh user data: $e');
    }
  }

  Future<void> _clearAuthData() async {
    await StorageService.clearAuthData();
    _user = null;
    _isAuthenticated = false;
    _error = null;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Helper methods
  bool get hasValidUser => _user != null && _isAuthenticated;
  
  String get userName => _user?.fullName ?? 'User';
  
  String get userEmail => _user?.email ?? '';
  
  String get userInitials => _user?.initials ?? 'U';
  
  double? get userBMI => _user?.bmi;
  
  String get userBMICategory => _user?.bmiCategory ?? 'Unknown';
  
  double? get weightToLose => _user?.weightToLose;
  
  bool get hasWeightGoal => _user?.targetWeight != null;
  
  // Progress tracking
  double get weightProgress {
    if (_user?.weight == null || _user?.targetWeight == null) return 0.0;
    
    final currentWeight = _user!.weight!;
    final targetWeight = _user!.targetWeight!;
    final startWeight = currentWeight; // This should ideally be stored separately
    
    if (startWeight <= targetWeight) return 100.0; // Already at or below target
    
    final totalWeightToLose = startWeight - targetWeight;
    final weightLost = startWeight - currentWeight;
    
    return (weightLost / totalWeightToLose * 100).clamp(0.0, 100.0);
  }
  
  String get weightProgressText {
    if (_user?.weight == null || _user?.targetWeight == null) {
      return 'No weight goal set';
    }
    
    final weightLost = weightToLose ?? 0;
    if (weightLost <= 0) {
      return 'Goal achieved!';
    }
    
    return '${weightLost.toStringAsFixed(1)} kg to go';
  }
}
