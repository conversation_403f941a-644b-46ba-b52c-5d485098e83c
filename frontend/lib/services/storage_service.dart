import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/constants.dart';
import '../models/user.dart';

class StorageService {
  static SharedPreferences? _prefs;

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  static SharedPreferences get prefs {
    if (_prefs == null) {
      throw Exception('StorageService not initialized. Call StorageService.init() first.');
    }
    return _prefs!;
  }

  // Auth Token
  static Future<void> saveAuthToken(String token) async {
    await prefs.setString(Constants.authTokenKey, token);
  }

  static String? getAuthToken() {
    return prefs.getString(Constants.authTokenKey);
  }

  static Future<void> removeAuthToken() async {
    await prefs.remove(Constants.authTokenKey);
  }

  // Session Token
  static Future<void> saveSessionToken(String token) async {
    await prefs.setString(Constants.sessionTokenKey, token);
  }

  static String? getSessionToken() {
    return prefs.getString(Constants.sessionTokenKey);
  }

  static Future<void> removeSessionToken() async {
    await prefs.remove(Constants.sessionTokenKey);
  }

  // User Data
  static Future<void> saveUser(User user) async {
    final userJson = jsonEncode(user.toJson());
    await prefs.setString(Constants.userDataKey, userJson);
  }

  static User? getUser() {
    final userJson = prefs.getString(Constants.userDataKey);
    if (userJson != null) {
      try {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        return User.fromJson(userMap);
      } catch (e) {
        // If parsing fails, remove corrupted data
        removeUser();
        return null;
      }
    }
    return null;
  }

  static Future<void> removeUser() async {
    await prefs.remove(Constants.userDataKey);
  }

  // Device Fingerprint
  static Future<void> saveDeviceFingerprint(String fingerprint) async {
    await prefs.setString(Constants.deviceFingerprintKey, fingerprint);
  }

  static String? getDeviceFingerprint() {
    return prefs.getString(Constants.deviceFingerprintKey);
  }

  // Video Progress Cache
  static Future<void> saveVideoProgress(int videoId, Map<String, dynamic> progress) async {
    final key = 'video_progress_$videoId';
    final progressJson = jsonEncode(progress);
    await prefs.setString(key, progressJson);
  }

  static Map<String, dynamic>? getVideoProgress(int videoId) {
    final key = 'video_progress_$videoId';
    final progressJson = prefs.getString(key);
    if (progressJson != null) {
      try {
        return jsonDecode(progressJson) as Map<String, dynamic>;
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // App Settings
  static Future<void> saveAppSetting(String key, dynamic value) async {
    if (value is String) {
      await prefs.setString(key, value);
    } else if (value is int) {
      await prefs.setInt(key, value);
    } else if (value is double) {
      await prefs.setDouble(key, value);
    } else if (value is bool) {
      await prefs.setBool(key, value);
    } else if (value is List<String>) {
      await prefs.setStringList(key, value);
    } else {
      // For complex objects, store as JSON
      await prefs.setString(key, jsonEncode(value));
    }
  }

  static T? getAppSetting<T>(String key) {
    final value = prefs.get(key);
    if (value is T) {
      return value;
    }
    return null;
  }

  // Notification Settings
  static Future<void> saveNotificationEnabled(bool enabled) async {
    await prefs.setBool('notifications_enabled', enabled);
  }

  static bool getNotificationEnabled() {
    return prefs.getBool('notifications_enabled') ?? true;
  }

  // Last Sync Time
  static Future<void> saveLastSyncTime(DateTime time) async {
    await prefs.setString('last_sync_time', time.toIso8601String());
  }

  static DateTime? getLastSyncTime() {
    final timeString = prefs.getString('last_sync_time');
    if (timeString != null) {
      try {
        return DateTime.parse(timeString);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // Clear all data
  static Future<void> clearAll() async {
    await prefs.clear();
  }

  // Clear auth data only
  static Future<void> clearAuthData() async {
    await removeAuthToken();
    await removeSessionToken();
    await removeUser();
  }

  // Check if user is logged in
  static bool isLoggedIn() {
    final token = getAuthToken();
    final user = getUser();
    return token != null && user != null;
  }

  // Get all stored keys (for debugging)
  static Set<String> getAllKeys() {
    return prefs.getKeys();
  }

  // Check storage size (approximate)
  static int getStorageSize() {
    int totalSize = 0;
    for (String key in prefs.getKeys()) {
      final value = prefs.get(key);
      if (value is String) {
        totalSize += value.length;
      }
    }
    return totalSize;
  }
}
