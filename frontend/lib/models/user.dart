class User {
  final int id;
  final String email;
  final String? phone;
  final String fullName;
  final int? age;
  final double? weight;
  final double? height;
  final double? targetWeight;
  final String uniqueToken;
  final String? deviceFingerprint;
  final bool isActive;
  final DateTime? lastLogin;
  final DateTime createdAt;
  final DateTime updatedAt;

  User({
    required this.id,
    required this.email,
    this.phone,
    required this.fullName,
    this.age,
    this.weight,
    this.height,
    this.targetWeight,
    required this.uniqueToken,
    this.deviceFingerprint,
    required this.isActive,
    this.lastLogin,
    required this.createdAt,
    required this.updatedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as int,
      email: json['email'] as String,
      phone: json['phone'] as String?,
      fullName: json['full_name'] as String,
      age: json['age'] as int?,
      weight: json['weight'] != null ? double.parse(json['weight'].toString()) : null,
      height: json['height'] != null ? double.parse(json['height'].toString()) : null,
      targetWeight: json['target_weight'] != null ? double.parse(json['target_weight'].toString()) : null,
      uniqueToken: json['unique_token'] as String,
      deviceFingerprint: json['device_fingerprint'] as String?,
      isActive: json['is_active'] == 1 || json['is_active'] == true,
      lastLogin: json['last_login'] != null ? DateTime.parse(json['last_login']) : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'phone': phone,
      'full_name': fullName,
      'age': age,
      'weight': weight,
      'height': height,
      'target_weight': targetWeight,
      'unique_token': uniqueToken,
      'device_fingerprint': deviceFingerprint,
      'is_active': isActive,
      'last_login': lastLogin?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  User copyWith({
    int? id,
    String? email,
    String? phone,
    String? fullName,
    int? age,
    double? weight,
    double? height,
    double? targetWeight,
    String? uniqueToken,
    String? deviceFingerprint,
    bool? isActive,
    DateTime? lastLogin,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      fullName: fullName ?? this.fullName,
      age: age ?? this.age,
      weight: weight ?? this.weight,
      height: height ?? this.height,
      targetWeight: targetWeight ?? this.targetWeight,
      uniqueToken: uniqueToken ?? this.uniqueToken,
      deviceFingerprint: deviceFingerprint ?? this.deviceFingerprint,
      isActive: isActive ?? this.isActive,
      lastLogin: lastLogin ?? this.lastLogin,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  double? get bmi {
    if (weight != null && height != null && height! > 0) {
      final heightInMeters = height! / 100;
      return weight! / (heightInMeters * heightInMeters);
    }
    return null;
  }

  String get bmiCategory {
    final bmiValue = bmi;
    if (bmiValue == null) return 'Unknown';
    
    if (bmiValue < 18.5) return 'Underweight';
    if (bmiValue < 25) return 'Normal';
    if (bmiValue < 30) return 'Overweight';
    return 'Obese';
  }

  double? get weightToLose {
    if (weight != null && targetWeight != null) {
      return weight! - targetWeight!;
    }
    return null;
  }

  String get initials {
    final names = fullName.split(' ');
    if (names.length >= 2) {
      return '${names[0][0]}${names[1][0]}'.toUpperCase();
    }
    return fullName.isNotEmpty ? fullName[0].toUpperCase() : 'U';
  }
}
