<?php
/**
 * Test Authentication Link Generation
 */

require_once 'backend/config/config.php';
require_once 'backend/config/database.php';
require_once 'backend/includes/Auth.php';

try {
    $auth = new Auth();
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if we have any users
    $stmt = $db->query("SELECT * FROM users WHERE is_active = 1 LIMIT 1");
    $user = $stmt->fetch();
    
    if (!$user) {
        // Create a test user
        $uniqueToken = generateUniqueToken(32);
        $stmt = $db->prepare("
            INSERT INTO users (email, full_name, unique_token, created_by) 
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([
            '<EMAIL>',
            'Test User',
            $uniqueToken,
            1 // Admin ID
        ]);
        $userId = $db->lastInsertId();
        echo "✅ Created test user with ID: $userId\n";
    } else {
        $userId = $user['id'];
        echo "✅ Using existing user: {$user['full_name']} (ID: $userId)\n";
    }
    
    // Generate authentication link
    $authLink = $auth->generateAuthLink($userId, 1); // Admin ID = 1
    
    if ($authLink) {
        echo "✅ Authentication link generated successfully!\n";
        echo "🔗 Link: $authLink\n";
        echo "\n";
        echo "📋 Test Instructions:\n";
        echo "1. Copy the link above\n";
        echo "2. Open it in your browser\n";
        echo "3. It should automatically log you into the Flutter app\n";
        echo "\n";
        echo "🌐 Frontend URL: http://localhost:3000\n";
        echo "🔧 Backend URL: http://localhost:8000\n";
    } else {
        echo "❌ Failed to generate authentication link\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
